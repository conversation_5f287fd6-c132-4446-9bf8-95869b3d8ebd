#include "FileSystemInfoDuck.h"
#include <QDateTime>
#include <QDebug>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <cstdio>

FileSystemInfoDuck::FileSystemInfoDuck()
    : m_id(0), m_fileSize(0), m_mftEntry(0), m_isDirectory(false), m_isDeleted(false) {
    initializeTimestamps();
}

FileSystemInfoDuck::FileSystemInfoDuck(const std::string &fileName, const std::string &filePath,
                                       long long fileSize, long long mftEntry, bool isDirectory,
                                       bool isDeleted, const std::string &diskIdentifier)
    : m_id(0), m_fileName(fileName), m_filePath(filePath), m_fileSize(fileSize),
      m_mftEntry(mftEntry), m_isDirectory(isDirectory), m_isDeleted(isDeleted),
      m_diskIdentifier(diskIdentifier) {
    initializeTimestamps();
    updateScanTime();
}

FileSystemInfoDuck::FileSystemInfoDuck(int id, const std::string &fileName, const std::string &filePath,
                                       long long fileSize, long long mftEntry, bool isDirectory,
                                       bool isDeleted, const std::string &createdTime,
                                       const std::string &modifiedTime, const std::string &accessedTime,
                                       const std::string &diskIdentifier, const std::string &scanTime)
    : m_id(id), m_fileName(fileName), m_filePath(filePath), m_fileSize(fileSize),
      m_mftEntry(mftEntry), m_isDirectory(isDirectory), m_isDeleted(isDeleted),
      m_createdTime(createdTime), m_modifiedTime(modifiedTime), m_accessedTime(accessedTime),
      m_diskIdentifier(diskIdentifier), m_scanTime(scanTime) {
}

FileSystemInfoDuck::~FileSystemInfoDuck() {
}

void FileSystemInfoDuck::updateScanTime() {
    QDateTime currentTime = QDateTime::currentDateTime();
    m_scanTime = currentTime.toString(Qt::ISODate).toStdString();
}

QString FileSystemInfoDuck::toString() const {
    return QString("FileSystemInfoDuck(id=%1, fileName='%2', filePath='%3', fileSize=%4, "
                   "mftEntry=%5, isDirectory=%6, isDeleted=%7, diskIdentifier='%8')")
            .arg(m_id)
            .arg(QString::fromStdString(m_fileName))
            .arg(QString::fromStdString(m_filePath))
            .arg(m_fileSize)
            .arg(m_mftEntry)
            .arg(m_isDirectory ? "true" : "false")
            .arg(m_isDeleted ? "true" : "false")
            .arg(QString::fromStdString(m_diskIdentifier));
}

bool FileSystemInfoDuck::operator==(const FileSystemInfoDuck &other) const {
    return m_id == other.m_id &&
           m_fileName == other.m_fileName &&
           m_filePath == other.m_filePath &&
           m_fileSize == other.m_fileSize &&
           m_mftEntry == other.m_mftEntry &&
           m_isDirectory == other.m_isDirectory &&
           m_isDeleted == other.m_isDeleted &&
           m_diskIdentifier == other.m_diskIdentifier;
}

bool FileSystemInfoDuck::operator!=(const FileSystemInfoDuck &other) const {
    return !(*this == other);
}

FileSystemInfoDuck FileSystemInfoDuck::fromMFTRecord(const void *mftRecord, const std::string &diskIdentifier) {
    // 这里应该实现从MFT记录解析文件信息的逻辑
    // 目前返回一个空对象作为占位符
    FileSystemInfoDuck info;
    info.setDiskIdentifier(diskIdentifier);
    return info;
}

std::string FileSystemInfoDuck::formatTimestamp(uint64_t timestamp) {
    if (timestamp == 0) {
        return "1970-01-01T00:00:00"; // 返回默认时间而不是空字符串
    }

    // 高性能时间戳格式化 - 避免QDateTime的开销
    time_t time = static_cast<time_t>(timestamp);
    struct tm* utc_tm = gmtime(&time);

    if (!utc_tm) {
        return "1970-01-01T00:00:00";
    }

    // 使用sprintf直接格式化，比QDateTime快10倍以上
    char buffer[32];
    snprintf(buffer, sizeof(buffer), "%04d-%02d-%02dT%02d:%02d:%02d",
             utc_tm->tm_year + 1900,
             utc_tm->tm_mon + 1,
             utc_tm->tm_mday,
             utc_tm->tm_hour,
             utc_tm->tm_min,
             utc_tm->tm_sec);

    return std::string(buffer);
}

std::vector<std::string> FileSystemInfoDuck::toDuckDBValues() const {
    std::vector<std::string> values;
    values.reserve(11);

    // 按照数据库表结构顺序，但跳过ID（自增）：
    // file_name, file_path, file_size, mft_entry, is_directory, is_deleted,
    // created_time, modified_time, accessed_time, disk_identifier, scan_time
    values.push_back(m_fileName);
    values.push_back(m_filePath);
    values.push_back(std::to_string(m_fileSize));
    values.push_back(std::to_string(m_mftEntry));
    values.push_back(m_isDirectory ? "true" : "false");
    values.push_back(m_isDeleted ? "true" : "false");
    values.push_back(m_createdTime);
    values.push_back(m_modifiedTime);
    values.push_back(m_accessedTime);
    values.push_back(m_diskIdentifier);
    values.push_back(m_scanTime);

    return values;
}

void FileSystemInfoDuck::initializeTimestamps() {
    QDateTime currentTime = QDateTime::currentDateTime();
    std::string currentTimeStr = currentTime.toString(Qt::ISODate).toStdString();
    
    if (m_createdTime.empty()) {
        m_createdTime = currentTimeStr;
    }
    if (m_modifiedTime.empty()) {
        m_modifiedTime = currentTimeStr;
    }
    if (m_accessedTime.empty()) {
        m_accessedTime = currentTimeStr;
    }
    if (m_scanTime.empty()) {
        m_scanTime = currentTimeStr;
    }
}
