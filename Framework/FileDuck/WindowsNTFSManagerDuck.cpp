#include "WindowsNTFSManagerDuck.h"
#include <QDebug>
#include <QMutexLocker>
#include <QDateTime>
#include <algorithm>

#ifdef _WIN32
#include <windows.h>
#include <winioctl.h>
#include <iostream>
#include <iomanip>
#endif

WindowsNTFSManagerDuck::WindowsNTFSManagerDuck(QObject *parent)
    : QObject(parent)
#ifdef _WIN32
    , m_volumeHandle(INVALID_HANDLE_VALUE)
    , m_mftHandle(INVALID_HANDLE_VALUE)
#endif
{
    // 初始化FileSystemInfo结构
    m_fsInfo.volumeName.clear();
    m_fsInfo.fileSystem.clear();
    m_fsInfo.totalSize = 0;
    m_fsInfo.freeSize = 0;
    m_fsInfo.bytesPerSector = 0;
    m_fsInfo.sectorsPerCluster = 0;
    m_fsInfo.totalClusters = 0;
    m_fsInfo.mftStartLcn = 0;
    m_fsInfo.mftSize = 0;

    qDebug() << "WindowsNTFSManagerDuck created";
}

WindowsNTFSManagerDuck::~WindowsNTFSManagerDuck() {
    close();
    qDebug() << "WindowsNTFSManagerDuck destroyed";
}

WindowsNTFSManagerDuck::WindowsNTFSManagerDuck(WindowsNTFSManagerDuck &&other) noexcept
    : QObject(other.parent()) {
    moveFrom(std::move(other));
}

WindowsNTFSManagerDuck &WindowsNTFSManagerDuck::operator=(WindowsNTFSManagerDuck &&other) noexcept {
    if (this != &other) {
        close();
        moveFrom(std::move(other));
    }
    return *this;
}

bool WindowsNTFSManagerDuck::openVolume(const std::string &driveLetter) {
    qDebug() << "WindowsNTFSManagerDuck::openVolume called with drive:" << QString::fromStdString(driveLetter);

    QMutexLocker locker(&m_mutex);

    qDebug() << "Closing previous volume...";
    closeVolume();
    resetInternal();

    m_driveLetter = driveLetter;

#ifdef _WIN32
    std::string volumePath = "\\\\.\\" + driveLetter + ":";
    qDebug() << "Opening volume path:" << QString::fromStdString(volumePath);

    m_volumeHandle = CreateFileA(
        volumePath.c_str(),
        GENERIC_READ,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        nullptr,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        nullptr
    );

    if (m_volumeHandle == INVALID_HANDLE_VALUE) {
        DWORD error = GetLastError();
        setError("Failed to open volume: " + volumePath + " Error: " + getWindowsErrorMessage(error));
        qDebug() << "Failed to open volume, error:" << error;
        return false;
    }

    qDebug() << "Volume opened successfully, reading file system info...";

    // 读取文件系统信息
    if (!readFileSystemInfo()) {
        closeVolume();
        return false;
    }

    // 检查是否为NTFS（直接检查，避免死锁）
    qDebug() << "Checking if volume is NTFS...";
    if (m_fsInfo.fileSystem != "NTFS") {
        setError("Volume is not NTFS: " + driveLetter);
        closeVolume();
        return false;
    }

    qDebug() << "Successfully opened NTFS volume:" << QString::fromStdString(driveLetter);
    qDebug() << "openVolume() completed successfully";
    return true;
#else
    setError("Windows NTFS Manager is only supported on Windows platform");
    return false;
#endif
}

bool WindowsNTFSManagerDuck::isNTFS() const {
    QMutexLocker locker(&m_mutex);
    return m_fsInfo.fileSystem == "NTFS";
}

WindowsNTFSManagerDuck::FileSystemInfo WindowsNTFSManagerDuck::getFileSystemInfo() const {
    QMutexLocker locker(&m_mutex);
    return m_fsInfo;
}

uint64_t WindowsNTFSManagerDuck::getMFTSize() const {
    qDebug() << "WindowsNTFSManagerDuck::getMFTSize() called";
    QMutexLocker locker(&m_mutex);
    qDebug() << "Returning MFT size:" << m_fsInfo.mftSize;
    return m_fsInfo.mftSize;
}

bool WindowsNTFSManagerDuck::readMFTRecord(uint64_t mftEntry, MFTRecordInfo &recordInfo) {
    QMutexLocker locker(&m_mutex);
    return readMFTRecordInternal(mftEntry, recordInfo);
}

bool WindowsNTFSManagerDuck::readMFTRecordInternal(uint64_t mftEntry, MFTRecordInfo &recordInfo) {
#ifdef _WIN32
    if (m_volumeHandle == INVALID_HANDLE_VALUE) {
        setError("Volume not opened");
        return false;
    }

    // MFT记录大小通常是1024字节，但我们需要按扇区对齐读取
    const size_t MFT_RECORD_SIZE = 1024;
    const DWORD SECTOR_SIZE = m_fsInfo.bytesPerSector;

    // 计算需要读取的扇区数（向上取整）
    DWORD sectorsToRead = (MFT_RECORD_SIZE + SECTOR_SIZE - 1) / SECTOR_SIZE;
    DWORD readSize = sectorsToRead * SECTOR_SIZE;

    std::vector<uint8_t> buffer(readSize);

    // 计算MFT记录的物理位置
    uint64_t mftOffset = m_fsInfo.mftStartLcn * m_fsInfo.sectorsPerCluster * m_fsInfo.bytesPerSector;
    uint64_t recordOffset = mftOffset + (mftEntry * MFT_RECORD_SIZE);

    // 确保偏移量按扇区对齐
    uint64_t alignedOffset = (recordOffset / SECTOR_SIZE) * SECTOR_SIZE;
    uint32_t recordOffsetInBuffer = recordOffset - alignedOffset;

    // 设置文件指针
    LARGE_INTEGER offset;
    offset.QuadPart = alignedOffset;

    if (!SetFilePointerEx(m_volumeHandle, offset, nullptr, FILE_BEGIN)) {
        DWORD error = GetLastError();
        setError("Failed to seek to MFT record " + std::to_string(mftEntry) + ": " + getWindowsErrorMessage(error));
        return false;
    }

    // 读取MFT记录
    DWORD bytesRead = 0;
    if (!ReadFile(m_volumeHandle, buffer.data(), readSize, &bytesRead, nullptr) ||
        bytesRead != readSize) {
        DWORD error = GetLastError();
        setError("Failed to read MFT record " + std::to_string(mftEntry) + ": " + getWindowsErrorMessage(error));
        return false;
    }

    // 解析MFT记录（从缓冲区中的正确位置开始）
    return parseMFTRecord(buffer.data() + recordOffsetInBuffer, MFT_RECORD_SIZE, recordInfo);
#else
    setError("Windows NTFS Manager is only supported on Windows platform");
    return false;
#endif
}

uint64_t WindowsNTFSManagerDuck::readMFTRecords(uint64_t startEntry, uint64_t count,
                                               std::vector<MFTRecordInfo> &records,
                                               ProgressCallback progressCallback) {
    // 使用优化的批量读取方法
    return readMFTRecordsBatch(startEntry, count, records, progressCallback);
}

uint64_t WindowsNTFSManagerDuck::readMFTRecordsBatch(uint64_t startEntry, uint64_t count,
                                                    std::vector<MFTRecordInfo> &records,
                                                    ProgressCallback progressCallback) {
    qDebug() << "readMFTRecordsBatch called with startEntry:" << startEntry << "count:" << count;
    QMutexLocker locker(&m_mutex);

#ifdef _WIN32
    if (m_volumeHandle == INVALID_HANDLE_VALUE) {
        setError("Volume not opened");
        return 0;
    }

    records.clear();
    records.reserve(count);

    const size_t MFT_RECORD_SIZE = 1024;
    const DWORD SECTOR_SIZE = m_fsInfo.bytesPerSector;

    // 批量读取的大小（一次读取多个MFT记录）
    const size_t BATCH_SIZE = 1024; // 一次读取1024个记录
    size_t totalReadSize = BATCH_SIZE * MFT_RECORD_SIZE;

    // 确保读取大小按扇区对齐
    DWORD sectorsNeeded = (totalReadSize + SECTOR_SIZE - 1) / SECTOR_SIZE;
    DWORD alignedReadSize = sectorsNeeded * SECTOR_SIZE;

    std::vector<uint8_t> buffer(alignedReadSize);
    uint64_t successCount = 0;

    // 计算MFT起始位置
    uint64_t mftOffset = m_fsInfo.mftStartLcn * m_fsInfo.sectorsPerCluster * m_fsInfo.bytesPerSector;

    for (uint64_t processed = 0; processed < count; processed += BATCH_SIZE) {

        size_t currentBatchSize = std::min(BATCH_SIZE, static_cast<size_t>(count - processed));
        uint64_t currentStartEntry = startEntry + processed;

        // 计算当前批次的物理位置
        uint64_t recordOffset = mftOffset + (currentStartEntry * MFT_RECORD_SIZE);
        uint64_t alignedOffset = (recordOffset / SECTOR_SIZE) * SECTOR_SIZE;
        uint32_t recordOffsetInBuffer = recordOffset - alignedOffset;

        // 设置文件指针
        LARGE_INTEGER offset;
        offset.QuadPart = alignedOffset;

        if (!SetFilePointerEx(m_volumeHandle, offset, nullptr, FILE_BEGIN)) {
            DWORD error = GetLastError();
            setError("Failed to seek to MFT batch: " + getWindowsErrorMessage(error));
            break;
        }

        // 批量读取MFT记录
        DWORD bytesRead = 0;
        if (!ReadFile(m_volumeHandle, buffer.data(), alignedReadSize, &bytesRead, nullptr)) {
            DWORD error = GetLastError();
            // 对于超出文件末尾的读取，这是正常的
            if (error == ERROR_HANDLE_EOF || error == ERROR_INVALID_PARAMETER) {
                break; // 已到达MFT末尾
            }
            setError("Failed to read MFT batch: " + getWindowsErrorMessage(error));
            break;
        }

        // 检查是否读取了足够的数据
        if (bytesRead < recordOffsetInBuffer) {
            break; // 没有足够的数据
        }

        // 解析每个MFT记录
        size_t availableRecords = (bytesRead - recordOffsetInBuffer) / MFT_RECORD_SIZE;
        size_t recordsToProcess = std::min(currentBatchSize, availableRecords);

        for (size_t i = 0; i < recordsToProcess; ++i) {
            uint64_t bufferOffset = recordOffsetInBuffer + (i * MFT_RECORD_SIZE);
            MFTRecordInfo recordInfo;

            if (parseMFTRecord(buffer.data() + bufferOffset, MFT_RECORD_SIZE, recordInfo)) {
                recordInfo.mftEntry = currentStartEntry + i;
                records.push_back(recordInfo);
                successCount++;
            }
        }

        // 调用进度回调
        if (progressCallback && (processed % 10000 == 0 || processed + currentBatchSize >= count)) {
            std::string currentFile = records.empty() ? "" : records.back().filePath;
            progressCallback(processed + currentBatchSize, count, currentFile);
        }

        // 发送进度信号
        if (processed % 10000 == 0 || processed + currentBatchSize >= count) {
            std::string currentFile = records.empty() ? "" : records.back().filePath;
            emit scanProgress(processed + currentBatchSize, count, QString::fromStdString(currentFile));
        }
    }

    qDebug() << "Batch read completed. Success count:" << successCount << "out of" << count;
    return successCount;
#else
    setError("Windows NTFS Manager is only supported on Windows platform");
    return 0;
#endif
}

uint64_t WindowsNTFSManagerDuck::scanMFTToFileInfos(std::vector<FileSystemInfoDuck> &fileInfos,
                                                   ProgressCallback progressCallback) {
    uint64_t mftSize = getMFTSize();
    if (mftSize == 0) {
        setError("Failed to get MFT size");
        return 0;
    }
    
    return scanMFTSegment(0, mftSize, fileInfos, progressCallback);
}

uint64_t WindowsNTFSManagerDuck::scanMFTSegment(uint64_t startEntry, uint64_t endEntry,
                                               std::vector<FileSystemInfoDuck> &fileInfos,
                                               ProgressCallback progressCallback) {
    if (startEntry >= endEntry) {
        return 0;
    }
    
    uint64_t count = endEntry - startEntry;
    std::vector<MFTRecordInfo> records;
    
    uint64_t readCount = readMFTRecords(startEntry, count, records, progressCallback);
    
    fileInfos.clear();
    fileInfos.reserve(readCount);
    
    std::string diskIdentifier = m_driveLetter;
    
    for (const auto &record : records) {
        FileSystemInfoDuck fileInfo = mftRecordToFileInfo(record, diskIdentifier);
        fileInfos.push_back(fileInfo);
    }
    
    return fileInfos.size();
}

std::string WindowsNTFSManagerDuck::getLastError() const {
    QMutexLocker locker(&m_mutex);
    return m_lastError;
}

bool WindowsNTFSManagerDuck::isInitialized() const {
    QMutexLocker locker(&m_mutex);
#ifdef _WIN32
    return m_volumeHandle != INVALID_HANDLE_VALUE;
#else
    return false;
#endif
}

void WindowsNTFSManagerDuck::close() {
    QMutexLocker locker(&m_mutex);
    closeVolume();
    resetInternal();
}

void WindowsNTFSManagerDuck::reset() {
    QMutexLocker locker(&m_mutex);
    closeVolume();
    resetInternal();
}

std::vector<std::string> WindowsNTFSManagerDuck::getAvailableDrives() {
    std::vector<std::string> drives;
    
#ifdef _WIN32
    DWORD driveMask = GetLogicalDrives();
    
    for (int i = 0; i < 26; ++i) {
        if (driveMask & (1 << i)) {
            char driveLetter = 'A' + i;
            std::string drive(1, driveLetter);
            
            if (isDriveNTFS(drive)) {
                drives.push_back(drive);
            }
        }
    }
#endif
    
    return drives;
}

std::string WindowsNTFSManagerDuck::formatDrivePath(const std::string &driveLetter) {
    return driveLetter + ":\\";
}

bool WindowsNTFSManagerDuck::isDriveNTFS(const std::string &driveLetter) {
#ifdef _WIN32
    char fileSystemName[MAX_PATH + 1];
    DWORD serialNumber, maxComponentLen, fileSystemFlags;
    
    std::string rootPath = driveLetter + ":\\";
    
    BOOL result = GetVolumeInformationA(
        rootPath.c_str(),
        nullptr, 0,
        &serialNumber,
        &maxComponentLen,
        &fileSystemFlags,
        fileSystemName,
        sizeof(fileSystemName)
    );
    
    if (!result) {
        return false;
    }
    
    return std::string(fileSystemName) == "NTFS";
#else
    return false;
#endif
}

uint64_t WindowsNTFSManagerDuck::fileTimeToUnixTime(uint64_t fileTime) {
    // FILETIME是从1601年1月1日开始的100纳秒间隔数
    // Unix时间戳是从1970年1月1日开始的秒数
    const uint64_t EPOCH_DIFFERENCE = 11644473600ULL; // 1970-1601的秒数差
    const uint64_t TICKS_PER_SECOND = 10000000ULL;    // 100纳秒间隔每秒
    
    return (fileTime / TICKS_PER_SECOND) - EPOCH_DIFFERENCE;
}

FileSystemInfoDuck WindowsNTFSManagerDuck::mftRecordToFileInfo(const MFTRecordInfo &recordInfo, const std::string &diskIdentifier) {
    FileSystemInfoDuck fileInfo;
    
    fileInfo.setFileName(recordInfo.fileName);
    fileInfo.setFilePath(recordInfo.filePath);
    fileInfo.setFileSize(static_cast<long long>(recordInfo.fileSize));
    fileInfo.setMftEntry(static_cast<long long>(recordInfo.mftEntry));
    fileInfo.setIsDirectory(recordInfo.isDirectory);
    fileInfo.setIsDeleted(recordInfo.isDeleted);
    fileInfo.setDiskIdentifier(diskIdentifier);
    
    // 转换时间戳
    fileInfo.setCreatedTime(FileSystemInfoDuck::formatTimestamp(fileTimeToUnixTime(recordInfo.createdTime)));
    fileInfo.setModifiedTime(FileSystemInfoDuck::formatTimestamp(fileTimeToUnixTime(recordInfo.modifiedTime)));
    fileInfo.setAccessedTime(FileSystemInfoDuck::formatTimestamp(fileTimeToUnixTime(recordInfo.accessedTime)));
    
    fileInfo.updateScanTime();
    
    return fileInfo;
}

void WindowsNTFSManagerDuck::closeVolume() {
#ifdef _WIN32
    if (m_volumeHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(m_volumeHandle);
        m_volumeHandle = INVALID_HANDLE_VALUE;
    }

    if (m_mftHandle != INVALID_HANDLE_VALUE) {
        CloseHandle(m_mftHandle);
        m_mftHandle = INVALID_HANDLE_VALUE;
    }
#endif
}

void WindowsNTFSManagerDuck::setError(const std::string &error) {
    m_lastError = error;
    qWarning() << "WindowsNTFSManagerDuck error:" << QString::fromStdString(error);
}

void WindowsNTFSManagerDuck::moveFrom(WindowsNTFSManagerDuck &&other) noexcept {
#ifdef _WIN32
    m_volumeHandle = other.m_volumeHandle;
    m_mftHandle = other.m_mftHandle;
    other.m_volumeHandle = INVALID_HANDLE_VALUE;
    other.m_mftHandle = INVALID_HANDLE_VALUE;
#endif

    m_driveLetter = std::move(other.m_driveLetter);
    m_lastError = std::move(other.m_lastError);
    m_fsInfo = other.m_fsInfo;

    // 重置源对象的FileSystemInfo结构
    other.m_fsInfo.volumeName.clear();
    other.m_fsInfo.fileSystem.clear();
    other.m_fsInfo.totalSize = 0;
    other.m_fsInfo.freeSize = 0;
    other.m_fsInfo.bytesPerSector = 0;
    other.m_fsInfo.sectorsPerCluster = 0;
    other.m_fsInfo.totalClusters = 0;
    other.m_fsInfo.mftStartLcn = 0;
    other.m_fsInfo.mftSize = 0;
}

void WindowsNTFSManagerDuck::resetInternal() {
    m_driveLetter.clear();
    m_lastError.clear();

    // 重置FileSystemInfo结构
    m_fsInfo.volumeName.clear();
    m_fsInfo.fileSystem.clear();
    m_fsInfo.totalSize = 0;
    m_fsInfo.freeSize = 0;
    m_fsInfo.bytesPerSector = 0;
    m_fsInfo.sectorsPerCluster = 0;
    m_fsInfo.totalClusters = 0;
    m_fsInfo.mftStartLcn = 0;
    m_fsInfo.mftSize = 0;
}

bool WindowsNTFSManagerDuck::parseMFTRecord(const void *recordData, size_t recordSize, MFTRecordInfo &recordInfo) {
    if (!recordData || recordSize < 48) {  // MFT记录头最小大小
        return false;
    }

    const uint8_t *buffer = static_cast<const uint8_t*>(recordData);

    // 正确初始化记录
    recordInfo.fileName.clear();
    recordInfo.mftEntry = 0;
    recordInfo.fileSize = 0;
    recordInfo.createdTime = 0;
    recordInfo.modifiedTime = 0;
    recordInfo.accessedTime = 0;
    recordInfo.isDirectory = false;
    recordInfo.isDeleted = false;

    // 检查MFT记录签名 "FILE"
    if (memcmp(buffer, "FILE", 4) != 0) {
        return false;  // 不是有效的MFT记录
    }

    // 解析MFT记录头
    uint16_t updateSequenceOffset = *reinterpret_cast<const uint16_t*>(buffer + 4);
    uint16_t updateSequenceSize = *reinterpret_cast<const uint16_t*>(buffer + 6);
    uint16_t flags = *reinterpret_cast<const uint16_t*>(buffer + 22);
    uint32_t usedSize = *reinterpret_cast<const uint32_t*>(buffer + 24);

    // 检查记录是否在使用中
    recordInfo.isDeleted = !(flags & 0x01);  // 标志位0表示记录在使用中

    // 解析属性
    uint16_t firstAttributeOffset = *reinterpret_cast<const uint16_t*>(buffer + 20);
    const uint8_t *attrPtr = buffer + firstAttributeOffset;

    while (attrPtr < buffer + recordSize && attrPtr < buffer + usedSize) {
        uint32_t attrType = *reinterpret_cast<const uint32_t*>(attrPtr);

        if (attrType == 0xFFFFFFFF) {
            break;  // 属性列表结束
        }

        uint32_t attrLength = *reinterpret_cast<const uint32_t*>(attrPtr + 4);
        if (attrLength == 0 || attrPtr + attrLength > buffer + recordSize) {
            break;  // 无效的属性长度
        }

        // 解析不同类型的属性
        switch (attrType) {
            case 0x10:  // $STANDARD_INFORMATION
                parseStandardInformation(attrPtr, attrLength, recordInfo);
                break;
            case 0x30:  // $FILE_NAME
                parseFileName(attrPtr, attrLength, recordInfo);
                break;
            case 0x80:  // $DATA
                parseDataAttribute(attrPtr, attrLength, recordInfo);
                break;
        }

        attrPtr += attrLength;
    }

    return true;
}

std::string WindowsNTFSManagerDuck::buildFullPath(const std::string &fileName, uint64_t parentMftEntry) {
    // 这里应该实现根据父目录MFT条目构建完整路径的逻辑
    // 目前返回简单的路径
    Q_UNUSED(parentMftEntry)

    return m_driveLetter + ":\\" + fileName;
}

bool WindowsNTFSManagerDuck::readFileSystemInfo() {
#ifdef _WIN32
    qDebug() << "Reading file system info for drive:" << QString::fromStdString(m_driveLetter);

    // 获取卷信息
    std::string rootPath = m_driveLetter + ":\\";
    char volumeName[MAX_PATH + 1] = {0};  // 初始化为0
    char fileSystemName[MAX_PATH + 1] = {0};  // 初始化为0
    DWORD serialNumber;
    DWORD maxComponentLength;
    DWORD fileSystemFlags;

    qDebug() << "Getting volume information...";
    if (!GetVolumeInformationA(
        rootPath.c_str(),
        volumeName, sizeof(volumeName),
        &serialNumber,
        &maxComponentLength,
        &fileSystemFlags,
        fileSystemName, sizeof(fileSystemName)
    )) {
        DWORD error = GetLastError();
        setError("Failed to get volume information: " + getWindowsErrorMessage(error));
        return false;
    }

    m_fsInfo.volumeName = volumeName;
    m_fsInfo.fileSystem = fileSystemName;
    qDebug() << "File system type:" << QString::fromStdString(m_fsInfo.fileSystem);

    // 检查是否为NTFS
    if (m_fsInfo.fileSystem != "NTFS") {
        qDebug() << "readFileSystemInfo: File system is" << QString::fromStdString(m_fsInfo.fileSystem) << "not NTFS";
        setError("Volume is not NTFS file system");
        return false;
    }

    // 获取磁盘空间信息
    qDebug() << "Getting disk space information...";
    ULARGE_INTEGER freeBytesAvailable;
    ULARGE_INTEGER totalNumberOfBytes;
    ULARGE_INTEGER totalNumberOfFreeBytes;

    if (!GetDiskFreeSpaceExA(
        rootPath.c_str(),
        &freeBytesAvailable,
        &totalNumberOfBytes,
        &totalNumberOfFreeBytes
    )) {
        DWORD error = GetLastError();
        setError("Failed to get disk space information: " + getWindowsErrorMessage(error));
        return false;
    }

    m_fsInfo.totalSize = totalNumberOfBytes.QuadPart;
    m_fsInfo.freeSize = totalNumberOfFreeBytes.QuadPart;

    // 获取簇信息
    qDebug() << "Getting cluster information...";
    DWORD sectorsPerCluster;
    DWORD bytesPerSector;
    DWORD numberOfFreeClusters;
    DWORD totalNumberOfClusters;

    if (!GetDiskFreeSpaceA(
        rootPath.c_str(),
        &sectorsPerCluster,
        &bytesPerSector,
        &numberOfFreeClusters,
        &totalNumberOfClusters
    )) {
        DWORD error = GetLastError();
        setError("Failed to get cluster information: " + getWindowsErrorMessage(error));
        return false;
    }

    m_fsInfo.bytesPerSector = bytesPerSector;
    m_fsInfo.sectorsPerCluster = sectorsPerCluster;
    m_fsInfo.totalClusters = totalNumberOfClusters;
    qDebug() << "Bytes per sector:" << bytesPerSector << "Sectors per cluster:" << sectorsPerCluster;

    // 读取NTFS引导扇区以获取MFT信息
    qDebug() << "Reading NTFS boot sector...";
    if (!readNTFSBootSector()) {
        return false;
    }

    qDebug() << "readFileSystemInfo() completed successfully";
    return true;
#else
    setError("Windows NTFS Manager is only supported on Windows platform");
    return false;
#endif
}

bool WindowsNTFSManagerDuck::readNTFSBootSector() {
#ifdef _WIN32
    qDebug() << "Reading NTFS boot sector...";

    // 使用标准扇区大小（512字节）来读取引导扇区
    const DWORD SECTOR_SIZE = 512;

    // 分配对齐的缓冲区
    std::vector<uint8_t> sectorBuffer(SECTOR_SIZE);
    DWORD bytesRead = 0;

    // 读取第一个扇区（引导扇区）
    LARGE_INTEGER offset;
    offset.QuadPart = 0;

    qDebug() << "Seeking to boot sector...";
    if (!SetFilePointerEx(m_volumeHandle, offset, nullptr, FILE_BEGIN)) {
        DWORD error = GetLastError();
        setError("Failed to seek to boot sector: " + getWindowsErrorMessage(error));
        return false;
    }

    qDebug() << "Reading boot sector data...";
    if (!ReadFile(m_volumeHandle, sectorBuffer.data(), SECTOR_SIZE, &bytesRead, nullptr) ||
        bytesRead != SECTOR_SIZE) {
        DWORD error = GetLastError();
        setError("Failed to read boot sector: " + getWindowsErrorMessage(error));
        return false;
    }

    // 解析NTFS引导扇区
    qDebug() << "Parsing NTFS boot sector...";
    const uint8_t* buffer = sectorBuffer.data();

    // 验证NTFS签名（偏移量3-10）
    qDebug() << "Verifying NTFS signature...";
    if (memcmp(buffer + 3, "NTFS    ", 8) != 0) {
        setError("Invalid NTFS boot sector signature");
        return false;
    }

    // 提取MFT起始LCN（偏移量48-55，8字节）
    m_fsInfo.mftStartLcn = *reinterpret_cast<const uint64_t*>(buffer + 48);
    qDebug() << "MFT start LCN:" << m_fsInfo.mftStartLcn;

    // 尝试读取MFT的第一个记录来获取真实的MFT大小
    qDebug() << "Reading actual MFT size...";
    if (!readActualMFTSize()) {
        // 如果读取失败，使用保守的估算值
        qWarning() << "Failed to read actual MFT size, using conservative estimate";
        m_fsInfo.mftSize = std::min(static_cast<uint64_t>(100000), m_fsInfo.totalClusters / 16);
    }
    qDebug() << "MFT size:" << m_fsInfo.mftSize;
    qDebug() << "readNTFSBootSector() completed successfully";

    return true;
#else
    setError("Windows NTFS Manager is only supported on Windows platform");
    return false;
#endif
}

bool WindowsNTFSManagerDuck::readActualMFTSize() {
#ifdef _WIN32
    qDebug() << "readActualMFTSize() called";

    // 使用更保守的方法来估算MFT大小
    // 基于经验值：通常MFT大小约为磁盘总大小的0.1%-1%

    uint64_t diskSizeGB = m_fsInfo.totalSize / (1024 * 1024 * 1024);
    qDebug() << "Disk size in GB:" << diskSizeGB;

    uint64_t estimatedMFTSize;

    if (diskSizeGB < 100) {
        // 小磁盘：保守估算
        estimatedMFTSize = std::min(static_cast<uint64_t>(150000), m_fsInfo.totalClusters / 24);
        qDebug() << "Small disk, estimated MFT size:" << estimatedMFTSize;
    } else if (diskSizeGB < 500) {
        // 中等磁盘：适中估算
        estimatedMFTSize = std::min(static_cast<uint64_t>(400000), m_fsInfo.totalClusters / 16);
        qDebug() << "Medium disk, estimated MFT size:" << estimatedMFTSize;
    } else {
        // 大磁盘：稍微积极的估算
        estimatedMFTSize = std::min(static_cast<uint64_t>(800000), m_fsInfo.totalClusters / 10);
        qDebug() << "Large disk, estimated MFT size:" << estimatedMFTSize;
    }

    // 确保至少有一些基本的MFT条目
    estimatedMFTSize = std::max(estimatedMFTSize, static_cast<uint64_t>(1000));

    m_fsInfo.mftSize = estimatedMFTSize;
    qDebug() << "Final MFT size set to:" << m_fsInfo.mftSize;

    qDebug() << "readActualMFTSize() completed successfully";
    return true;
#else
    return false;
#endif
}

void WindowsNTFSManagerDuck::parseStandardInformation(const uint8_t *attrData, uint32_t attrLength, MFTRecordInfo &record) {
    if (attrLength < 72) return;  // $STANDARD_INFORMATION最小大小

    uint8_t nonResident = attrData[8];
    if (nonResident) return;  // 只处理常驻属性

    uint16_t attrOffset = *reinterpret_cast<const uint16_t*>(attrData + 20);
    const uint8_t *data = attrData + attrOffset;

    if (attrOffset + 72 > attrLength) return;

    // 读取时间戳（FILETIME格式）
    record.createdTime = *reinterpret_cast<const uint64_t*>(data + 0);
    record.modifiedTime = *reinterpret_cast<const uint64_t*>(data + 8);
    record.accessedTime = *reinterpret_cast<const uint64_t*>(data + 24);
    uint32_t attributes = *reinterpret_cast<const uint32_t*>(data + 32);

    // 检查是否为目录
    record.isDirectory = (attributes & FILE_ATTRIBUTE_DIRECTORY) != 0;
}

void WindowsNTFSManagerDuck::parseFileName(const uint8_t *attrData, uint32_t attrLength, MFTRecordInfo &record) {
    if (attrLength < 24) return;

    uint8_t nonResident = attrData[8];
    if (nonResident) return;  // 只处理常驻属性

    uint16_t attrOffset = *reinterpret_cast<const uint16_t*>(attrData + 20);
    const uint8_t *data = attrData + attrOffset;

    if (attrOffset + 66 > attrLength) return;

    uint8_t fileNameLength = data[64];
    uint8_t nameSpace = data[65];

    // 只处理WIN32和POSIX命名空间的文件名
    if (nameSpace != 1 && nameSpace != 3) return;

    if (attrOffset + 66 + (fileNameLength * 2) > attrLength) return;

    // 提取Unicode文件名
    const uint16_t *unicodeName = reinterpret_cast<const uint16_t*>(data + 66);

    // 创建wstring，需要正确处理类型转换
    std::wstring wFileName;
    wFileName.reserve(fileNameLength);
    for (uint8_t i = 0; i < fileNameLength; ++i) {
        wFileName.push_back(static_cast<wchar_t>(unicodeName[i]));
    }

    // 转换为UTF-8
    int utf8Length = WideCharToMultiByte(CP_UTF8, 0, wFileName.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (utf8Length > 0) {
        std::vector<char> utf8Buffer(utf8Length);
        WideCharToMultiByte(CP_UTF8, 0, wFileName.c_str(), -1, utf8Buffer.data(), utf8Length, nullptr, nullptr);
        record.fileName = std::string(utf8Buffer.data());
        record.filePath = buildFullPath(record.fileName, 0);  // 简化版本，不处理父目录
    }
}

void WindowsNTFSManagerDuck::parseDataAttribute(const uint8_t *attrData, uint32_t attrLength, MFTRecordInfo &record) {
    if (attrLength < 16) return;

    uint8_t nonResident = attrData[8];

    if (!nonResident) {
        // 常驻数据属性
        uint32_t attrSize = *reinterpret_cast<const uint32_t*>(attrData + 16);
        record.fileSize = attrSize;
    } else {
        // 非常驻数据属性
        if (attrLength >= 64) {
            uint64_t allocatedSize = *reinterpret_cast<const uint64_t*>(attrData + 40);
            uint64_t realSize = *reinterpret_cast<const uint64_t*>(attrData + 48);
            record.fileSize = realSize;
        }
    }
}

#ifdef _WIN32
std::string WindowsNTFSManagerDuck::getWindowsErrorMessage(DWORD errorCode) {
    LPSTR messageBuffer = nullptr;
    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        nullptr, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPSTR)&messageBuffer, 0, nullptr);

    std::string message(messageBuffer, size);
    LocalFree(messageBuffer);

    return message;
}
#endif
