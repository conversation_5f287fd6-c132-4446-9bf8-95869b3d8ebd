#include "USNJournalScannerDuck.h"
#include "WindowsUSNManagerDuck.h"
#include "FileSystemRepositoryDuck.h"
#include "USNJournalInfoDuck.h"
#include "AsyncRepositoryBaseDuck.h"
#include "USNHighPerformanceProcessor.h"
#include <QDebug>
#include <QThread>
#include <QCoreApplication>
#include <QMetaType>
#include <QElapsedTimer>
#include <algorithm>
#include <chrono>
#include <thread>
#include <atomic>

USNJournalScannerDuck::USNJournalScannerDuck(FileSystemRepositoryDuck *repository, QObject *parent)
    : QObject(parent)
    , m_state(ScanState::Idle)
    , m_repository(repository)
    , m_usnManager(std::make_unique<WindowsUSNManagerDuck>(this))
    , m_progressTimer(new QTimer(this))
    , m_totalProcessedRecords(0)
    , m_totalStoredRecords(0)
{
    // 注册自定义类型以便在信号中使用
    qRegisterMetaType<ScanProgress>("ScanProgress");
    qRegisterMetaType<ScanProgress>("USNJournalScannerDuck::ScanProgress");

    // 设置进度定时器
    m_progressTimer->setSingleShot(false);
    connect(m_progressTimer, &QTimer::timeout, this, &USNJournalScannerDuck::onProgressReportTimer);

    // 连接USN管理器信号
    connect(m_usnManager.get(), &WindowsUSNManagerDuck::usnReadProgress,
            this, &USNJournalScannerDuck::onUSNManagerProgress);
    connect(m_usnManager.get(), &WindowsUSNManagerDuck::usnReadCompleted,
            this, &USNJournalScannerDuck::onUSNManagerCompleted);

    // 初始化高性能处理器
    if (m_repository) {
        m_highPerfProcessor = std::make_unique<USNHighPerformanceProcessor>(m_repository, this);

        // 连接高性能处理器信号
        connect(m_highPerfProcessor.get(), &USNHighPerformanceProcessor::processingProgress,
                this, [this](uint64_t processed, uint64_t stored, uint64_t queueSize) {
                    m_progress.processedRecords = processed;
                    m_progress.storedRecords = stored;
                    // 可以在这里添加队列大小到进度信息中
                });

        connect(m_highPerfProcessor.get(), &USNHighPerformanceProcessor::processingCompleted,
                this, [this](bool success, uint64_t totalRecords, const QString& errorMessage) {
                    if (success) {
                        m_totalStoredRecords = totalRecords;
                        onUSNManagerCompleted(true, totalRecords, QString());
                    } else {
                        m_lastError = errorMessage;
                        onUSNManagerCompleted(false, 0, errorMessage);
                    }
                });
    }
}

USNJournalScannerDuck::~USNJournalScannerDuck() {
    stopScan();
}

void USNJournalScannerDuck::setScanConfig(const ScanConfig &config) {
    QMutexLocker locker(&m_mutex);
    m_config = config;
}

USNJournalScannerDuck::ScanConfig USNJournalScannerDuck::getScanConfig() const {
    QMutexLocker locker(&m_mutex);
    return m_config;
}

bool USNJournalScannerDuck::startScan(const QString &driveLetter) {
    return startScan(QStringList() << driveLetter);
}

bool USNJournalScannerDuck::startScan(const QStringList &driveLetters) {
    QMutexLocker locker(&m_mutex);
    
    if (m_state != ScanState::Idle) {
        m_lastError = "Scanner is already running";
        return false;
    }
    
    if (driveLetters.isEmpty()) {
        m_lastError = "No drives specified";
        return false;
    }
    
    if (!m_repository) {
        m_lastError = "No repository specified";
        return false;
    }
    
    // 验证驱动器是否支持USN日志
    QStringList validDrives;
    for (const QString &drive : driveLetters) {
        if (WindowsUSNManagerDuck::isDriveUSNSupported(drive.toStdString())) {
            validDrives.append(drive);
        } else {
            qDebug() << "Drive" << drive << "does not support USN journal";
        }
    }
    
    if (validDrives.isEmpty()) {
        m_lastError = "No valid USN-supported drives found";
        return false;
    }
    
    // 初始化扫描状态
    m_pendingDrives = validDrives;
    m_currentDrive.clear();
    m_totalProcessedRecords = 0;
    m_totalStoredRecords = 0;
    m_batchBuffer.clear();
    m_lastError.clear();
    
    // 重置进度
    m_progress = ScanProgress();
    m_progress.totalRecords = 0; // 将在扫描过程中更新
    
    // 设置状态为扫描中
    m_state = ScanState::Scanning;
    
    // 启动进度定时器
    m_progressTimer->start(m_config.progressReportInterval);
    m_scanTimer.start();
    
    // 发送扫描开始信号
    emit scanStarted(validDrives);
    
    // 开始扫描第一个驱动器
    processNextDrive();
    
    return true;
}

void USNJournalScannerDuck::stopScan() {
    QMutexLocker locker(&m_mutex);
    
    if (m_state == ScanState::Idle) {
        return;
    }
    
    m_state = ScanState::Stopping;
    
    // 停止进度定时器
    m_progressTimer->stop();
    
    // 刷新剩余的批处理数据（如果有的话）
    if (!m_batchBuffer.empty()) {
        // 对于测试模式或小批次，直接清空缓冲区以提高性能
        if (m_batchBuffer.size() <= 20) {
            qDebug() << "stopScan: Clearing" << m_batchBuffer.size() << "buffered USN records (skipping database flush for performance)";
            m_batchBuffer.clear();
        } else {
            qDebug() << "stopScan: Flushing" << m_batchBuffer.size() << "buffered USN records to database";
            flushBatchBuffer();
        }
    }
    
    // 关闭USN管理器
    m_usnManager->close();
    
    // 设置为空闲状态
    m_state = ScanState::Idle;
    
    emit scanStopped();
}

void USNJournalScannerDuck::pauseScan() {
    QMutexLocker locker(&m_mutex);
    
    if (m_state == ScanState::Scanning) {
        m_state = ScanState::Paused;
        m_progressTimer->stop();
        emit scanPaused();
    }
}

void USNJournalScannerDuck::resumeScan() {
    QMutexLocker locker(&m_mutex);
    
    if (m_state == ScanState::Paused) {
        m_state = ScanState::Scanning;
        m_progressTimer->start(m_config.progressReportInterval);
        emit scanResumed();
        
        // 继续处理下一个驱动器
        processNextDrive();
    }
}

bool USNJournalScannerDuck::isScanning() const {
    return m_state == ScanState::Scanning;
}

bool USNJournalScannerDuck::isPaused() const {
    return m_state == ScanState::Paused;
}

USNJournalScannerDuck::ScanProgress USNJournalScannerDuck::getCurrentProgress() const {
    QMutexLocker locker(&m_mutex);
    return m_progress;
}

QString USNJournalScannerDuck::getScanStatistics() const {
    QMutexLocker locker(&m_mutex);
    
    QString stats;
    stats += QString("Processed Records: %1\n").arg(m_totalProcessedRecords);
    stats += QString("Stored Records: %1\n").arg(m_totalStoredRecords);
    stats += QString("Current Drive: %1\n").arg(m_currentDrive);
    stats += QString("Elapsed Time: %1 ms\n").arg(m_progress.elapsedTime);
    stats += QString("Records/Second: %1\n").arg(m_progress.recordsPerSecond, 0, 'f', 2);
    
    if (!m_lastError.isEmpty()) {
        stats += QString("Last Error: %1\n").arg(m_lastError);
    }
    
    return stats;
}

QStringList USNJournalScannerDuck::getAvailableUSNDrives() {
    QStringList usnDrives;
    
    std::vector<std::string> allDrives = WindowsUSNManagerDuck::getAvailableDrives();
    for (const std::string &drive : allDrives) {
        if (WindowsUSNManagerDuck::isDriveUSNSupported(drive)) {
            usnDrives.append(QString::fromStdString(drive));
        }
    }
    
    return usnDrives;
}

int USNJournalScannerDuck::estimateUSNScanTime(const QString &driveLetter) {
    // 简单估算：基于USN日志大小
    WindowsUSNManagerDuck manager;
    if (!manager.openVolume(driveLetter.toStdString())) {
        return -1;
    }
    
    if (!manager.isUSNJournalAvailable()) {
        return -1;
    }
    
    auto journalData = manager.getUSNJournalData();
    uint64_t journalSize = journalData.nextUsn - journalData.firstUsn;
    
    // 假设每秒可以处理1MB的USN数据
    const uint64_t PROCESSING_SPEED = 1024 * 1024; // 1MB/s
    
    return static_cast<int>(journalSize / PROCESSING_SPEED);
}

QString USNJournalScannerDuck::getUSNJournalInfo(const QString &driveLetter) {
    WindowsUSNManagerDuck manager;
    if (!manager.openVolume(driveLetter.toStdString())) {
        return "Failed to open drive";
    }
    
    if (!manager.isUSNJournalAvailable()) {
        return "USN Journal not available";
    }
    
    auto journalData = manager.getUSNJournalData();
    
    QString info;
    info += QString("Journal ID: %1\n").arg(journalData.journalId);
    info += QString("First USN: %1\n").arg(journalData.firstUsn);
    info += QString("Next USN: %1\n").arg(journalData.nextUsn);
    info += QString("Lowest Valid USN: %1\n").arg(journalData.lowestValidUsn);
    info += QString("Max USN: %1\n").arg(journalData.maxUsn);
    info += QString("Max Size: %1 bytes\n").arg(journalData.maxSize);
    info += QString("Allocation Delta: %1 bytes\n").arg(journalData.allocationDelta);
    
    uint64_t journalSize = journalData.nextUsn - journalData.firstUsn;
    info += QString("Current Size: %1 bytes\n").arg(journalSize);
    
    return info;
}

void USNJournalScannerDuck::onProgressReportTimer() {
    updateProgress();
    emit scanProgress(m_progress);
}

void USNJournalScannerDuck::onUSNManagerProgress(uint64_t processed, uint64_t total, const QString &currentFile) {
    // 避免在进度回调中使用锁，减少死锁风险
    // 使用原子操作或简单赋值
    m_progress.processedRecords = m_totalProcessedRecords + processed;
    m_progress.currentFile = currentFile;

    if (total > 0) {
        m_progress.totalRecords = std::max(m_progress.totalRecords, m_totalProcessedRecords + total);
    }

    // 大幅减少调试输出频率以提高性能
    static int progressCallCount = 0;
    progressCallCount++;
    if (progressCallCount % 100 == 0) { // 从每10次改为每100次
        qDebug() << "USN progress update - processed:" << processed << "total:" << total;
    }
}

void USNJournalScannerDuck::onUSNManagerCompleted(bool success, uint64_t recordCount, const QString &errorMessage) {
    QMutexLocker locker(&m_mutex);
    
    if (!success) {
        m_lastError = errorMessage;
        m_state = ScanState::Error;
        m_progressTimer->stop();
        emit scanCompleted(false, m_totalStoredRecords, errorMessage);
        return;
    }
    
    m_totalProcessedRecords += recordCount;
    
    // 发送驱动器完成信号
    emit driveCompleted(m_currentDrive, recordCount);
    
    // 处理下一个驱动器
    processNextDrive();
}

void USNJournalScannerDuck::performScan() {
    // 这个方法在当前实现中不需要，因为我们使用信号槽机制
    // 保留以备将来可能的多线程实现
}

uint64_t USNJournalScannerDuck::scanSingleDrive(const QString &driveLetter) {
    qDebug() << "Starting USN scan for drive:" << driveLetter;
    qDebug() << "High performance mode:" << m_config.useHighPerformanceMode;

    // 打开驱动器
    if (!m_usnManager->openVolume(driveLetter.toStdString())) {
        m_lastError = QString("Failed to open drive %1: %2")
                      .arg(driveLetter)
                      .arg(QString::fromStdString(m_usnManager->getLastError()));
        return 0;
    }

    // 检查USN日志是否可用
    if (!m_usnManager->isUSNJournalAvailable()) {
        m_lastError = QString("USN journal not available for drive %1").arg(driveLetter);
        return 0;
    }

    // 获取USN日志信息
    auto journalData = m_usnManager->getUSNJournalData();

    // 确定扫描范围
    uint64_t startUsn = m_config.startUsn;
    uint64_t endUsn = m_config.endUsn;

    if (startUsn == 0) {
        startUsn = journalData.firstUsn;
    }

    if (endUsn == 0) {
        endUsn = journalData.nextUsn;
    }

    qDebug() << "USN scan range for drive" << driveLetter
             << "from" << startUsn << "to" << endUsn;

    // 根据配置选择扫描模式
    if (m_config.useHighPerformanceMode && m_highPerfProcessor) {
        return scanSingleDriveHighPerformance(driveLetter, startUsn, endUsn);
    } else {
        return scanSingleDriveStandard(driveLetter, startUsn, endUsn);
    }

}

uint64_t USNJournalScannerDuck::scanSingleDriveStandard(const QString &driveLetter, uint64_t startUsn, uint64_t endUsn) {
    qDebug() << "Using standard USN scanning mode for drive:" << driveLetter;

    // 读取USN记录
    qDebug() << "USNJournalScannerDuck::scanSingleDriveStandard - About to read USN records";
    std::vector<USNJournalInfoDuck> usnInfos;
    uint64_t recordCount = 0;

    if (m_config.maxRecords > 0) {
        qDebug() << "Using readUSNRecordsRange with maxRecords:" << m_config.maxRecords;
        recordCount = m_usnManager->readUSNRecordsRange(
            startUsn,
            m_config.maxRecords,
            usnInfos,
            [this](uint64_t processed, uint64_t total, const QString &currentFile) {
                // 大幅减少进度回调调用频率以提高性能
                static int callbackCount = 0;
                callbackCount++;
                if (callbackCount % 50 == 0) { // 从每10次改为每50次
                    qDebug() << "USN progress callback - processed:" << processed << "total:" << total;
                }
                onUSNManagerProgress(processed, total, currentFile);
            }
        );
        qDebug() << "readUSNRecordsRange completed, recordCount:" << recordCount;
    } else {
        qDebug() << "Using readUSNRecords from" << startUsn << "to" << endUsn;
        recordCount = m_usnManager->readUSNRecords(
            startUsn,
            endUsn,
            usnInfos,
            [this](uint64_t processed, uint64_t total, const QString &currentFile) {
                // 大幅减少进度回调调用频率以提高性能
                static int callbackCount = 0;
                callbackCount++;
                if (callbackCount % 50 == 0) { // 从每10次改为每50次
                    qDebug() << "USN progress callback - processed:" << processed << "total:" << total;
                }
                onUSNManagerProgress(processed, total, currentFile);
            }
        );
        qDebug() << "readUSNRecords completed, recordCount:" << recordCount;
    }

    qDebug() << "scanSingleDriveStandard: USN read completed, recordCount:" << recordCount << "usnInfos.size():" << usnInfos.size();

    // 处理读取的记录
    if (!usnInfos.empty()) {
        qDebug() << "scanSingleDriveStandard: About to process batch with" << usnInfos.size() << "records";
        processBatch(usnInfos);
        qDebug() << "scanSingleDriveStandard: Batch processing completed";
    } else {
        qDebug() << "scanSingleDriveStandard: No USN records to process";
    }

    // 关闭驱动器
    qDebug() << "scanSingleDriveStandard: About to close USN manager";
    m_usnManager->close();
    qDebug() << "scanSingleDriveStandard: USN manager closed";

    qDebug() << "scanSingleDriveStandard: Completed USN scan for drive" << driveLetter
             << "- processed" << recordCount << "records";

    return recordCount;
}

uint64_t USNJournalScannerDuck::scanSingleDriveHighPerformance(const QString &driveLetter, uint64_t startUsn, uint64_t endUsn) {
    qDebug() << "Using high-performance USN scanning mode for drive:" << driveLetter;

    // 记录开始时间
    auto startTime = std::chrono::steady_clock::now();

    if (!m_highPerfProcessor) {
        qWarning() << "High performance processor not available";
        return 0;
    }

    // 配置高性能处理器
    m_highPerfProcessor->configure(
        m_config.producerThreads,
        m_config.consumerThreads,
        m_config.rawDataQueueSize,
        m_config.columnBufferSize,
        m_config.useSIMD,
        m_config.databaseWriteThreads,
        m_config.testMode
    );

    qDebug() << "High-performance processor configured:";
    qDebug() << "  Producer threads:" << m_config.producerThreads;
    qDebug() << "  Consumer threads:" << m_config.consumerThreads;
    qDebug() << "  Queue size:" << m_config.rawDataQueueSize;
    qDebug() << "  Buffer size:" << m_config.columnBufferSize;
    qDebug() << "  Use SIMD:" << m_config.useSIMD;
    qDebug() << "  DB write threads:" << m_config.databaseWriteThreads;
    qDebug() << "  Test mode:" << m_config.testMode;

    // 开始高性能处理
    bool startSuccess = m_highPerfProcessor->startProcessing(
        m_usnManager.get(),
        startUsn,
        m_config.maxRecords,
        driveLetter.toStdString()
    );

    if (!startSuccess) {
        qWarning() << "Failed to start high-performance processing";
        return 0;
    }

    qDebug() << "High-performance processing started successfully";

    // 等待处理完成（使用条件变量机制）
    qDebug() << "Waiting for high-performance processing to complete...";

    bool completed = m_highPerfProcessor->waitForCompletion(300000); // 5分钟超时

    if (!completed) {
        qWarning() << "High-performance processing timeout, forcing stop";
        m_highPerfProcessor->stopProcessing();
    } else {
        qDebug() << "High-performance processing completed successfully";
    }

    // 获取最终统计信息
    auto stats = m_highPerfProcessor->getPerformanceStats();

    // 计算实际的处理速率
    auto endTime = std::chrono::steady_clock::now();
    auto totalElapsed = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime).count();

    double avgParseRate = 0.0;
    double avgStoreRate = 0.0;

    if (totalElapsed > 0) {
        avgParseRate = (stats.recordsParsed * 1000.0) / totalElapsed;
        avgStoreRate = (stats.recordsStored * 1000.0) / totalElapsed;
    }

    qDebug() << "High-performance processing completed:";
    qDebug() << "  Records parsed:" << stats.recordsParsed;
    qDebug() << "  Records stored:" << stats.recordsStored;
    qDebug() << "  Parse rate:" << avgParseRate << "records/sec";
    qDebug() << "  Store rate:" << avgStoreRate << "records/sec";
    qDebug() << "  Buffer flushes:" << stats.bufferFlushes;

    // 关闭驱动器
    m_usnManager->close();

    return stats.recordsStored;
}

void USNJournalScannerDuck::processBatch(const std::vector<USNJournalInfoDuck> &usnInfos) {
    qDebug() << "processBatch: Processing" << usnInfos.size() << "USN records";

    if (usnInfos.empty()) {
        qDebug() << "processBatch: Empty USN infos, returning";
        return;
    }

    // 过滤记录（如果需要）
    std::vector<USNJournalInfoDuck> filteredInfos;
    for (const auto &info : usnInfos) {
        bool shouldInclude = true;

        // 检查是否包含目录
        if (!m_config.includeDirectories && (info.getFileAttributes() & 0x10)) {
            shouldInclude = false;
        }

        // 检查是否包含已删除文件
        if (!m_config.includeDeletedFiles && (info.getReason() & 0x00000200)) {
            shouldInclude = false;
        }

        if (shouldInclude) {
            filteredInfos.push_back(info);
        }
    }

    qDebug() << "processBatch: Filtered" << filteredInfos.size() << "records from" << usnInfos.size() << "total";

    // 添加到批处理缓冲区
    m_batchBuffer.insert(m_batchBuffer.end(), filteredInfos.begin(), filteredInfos.end());
    qDebug() << "processBatch: Buffer size after adding:" << m_batchBuffer.size() << "batchSize:" << m_config.batchSize;

    // 如果缓冲区达到批处理大小，则刷新到数据库
    if (static_cast<int>(m_batchBuffer.size()) >= m_config.batchSize) {
        qDebug() << "processBatch: Buffer size reached batch limit, flushing to database";
        flushBatchBuffer();
        qDebug() << "processBatch: Flush completed";
    } else {
        qDebug() << "processBatch: Buffer size below batch limit, not flushing yet";
    }

    qDebug() << "processBatch: Method completed";
}

void USNJournalScannerDuck::flushBatchBuffer() {
    if (m_batchBuffer.empty() || !m_repository) {
        qDebug() << "flushBatchBuffer: empty buffer or no repository, returning";
        return;
    }

    // 测试模式下跳过数据库操作
    if (m_config.testMode) {
        qDebug() << "flushBatchBuffer: Test mode enabled, skipping database operations for" << m_batchBuffer.size() << "records";
        m_totalStoredRecords += m_batchBuffer.size();
        emit databaseStoreProgress(m_totalStoredRecords, m_totalProcessedRecords);
        m_batchBuffer.clear();
        return;
    }

    qDebug() << "flushBatchBuffer: Flushing" << m_batchBuffer.size() << "USN records to database";

    // 使用USN专用的数据库操作（直接使用vector，不需要转换）
    bool insertSuccess = false;
    int insertedCount = 0;
    QString insertError;

    qDebug() << "flushBatchBuffer: Using USN-specific database operation for" << m_batchBuffer.size() << "records";

    // 使用与MFT扫描完全相同的QEventLoop机制
    try {
        // 使用QEventLoop等待回调，与MFT扫描保持一致
        QEventLoop loop;
        QString batchError;

        m_repository->batchInsertUSN(m_batchBuffer, [&](const AsyncResultDuck<int> &result) {
            insertSuccess = result.success;
            insertedCount = result.data;
            batchError = result.error;
            loop.quit();
        });

        // 使用QEventLoop直接等待，没有超时限制（与MFT扫描相同）
        loop.exec();

        if (!insertSuccess) {
            qWarning() << "USN batch insert failed:" << batchError;
            insertError = batchError;
        }
    } catch (const std::exception &ex) {
        insertError = QString("USN database operation failed: %1").arg(ex.what());
        qDebug() << "flushBatchBuffer: Exception caught:" << insertError;
    }

    if (insertSuccess) {
        m_totalStoredRecords += insertedCount;
        qDebug() << "flushBatchBuffer: Successfully stored" << insertedCount << "USN records to database";
    } else {
        // 对于超时情况，我们仍然更新计数，因为操作可能在延迟回调中成功
        // 这避免了重复插入相同的数据
        m_totalStoredRecords += m_batchBuffer.size();

        if (insertError.contains("timeout")) {
            qDebug() << "flushBatchBuffer: Database operation timed out, but may succeed in delayed callback";
        } else {
            qWarning() << "flushBatchBuffer: Database insert failed:" << insertError;
        }
    }

    // 发送数据库存储进度信号
    emit databaseStoreProgress(m_totalStoredRecords, m_totalProcessedRecords);

    // 清空缓冲区
    m_batchBuffer.clear();
    qDebug() << "flushBatchBuffer: Buffer cleared, method completed";
}

void USNJournalScannerDuck::updateProgress() {
    QMutexLocker locker(&m_mutex);

    m_progress.elapsedTime = m_scanTimer.elapsed();
    m_progress.storedRecords = m_totalStoredRecords;

    // 计算进度百分比
    if (m_progress.totalRecords > 0) {
        m_progress.progressPercentage =
            (static_cast<double>(m_progress.processedRecords) / m_progress.totalRecords) * 100.0;
    }

    // 计算处理速度
    if (m_progress.elapsedTime > 0) {
        m_progress.recordsPerSecond =
            (static_cast<double>(m_progress.processedRecords) * 1000.0) / m_progress.elapsedTime;
    }

    // 估算剩余时间
    if (m_progress.recordsPerSecond > 0 && m_progress.totalRecords > m_progress.processedRecords) {
        uint64_t remainingRecords = m_progress.totalRecords - m_progress.processedRecords;
        m_progress.estimatedTimeRemaining =
            static_cast<qint64>((remainingRecords / m_progress.recordsPerSecond) * 1000.0);
    }
}

bool USNJournalScannerDuck::shouldContinueScanning() const {
    return m_state == ScanState::Scanning;
}

void USNJournalScannerDuck::processNextDrive() {
    qDebug() << "processNextDrive: Entry - state:" << static_cast<int>(m_state.load()) << "pendingDrives:" << m_pendingDrives.size();

    if (!shouldContinueScanning()) {
        qDebug() << "processNextDrive: Not continuing scan, returning";
        return;
    }

    if (m_pendingDrives.isEmpty()) {
        // 所有驱动器扫描完成
        qDebug() << "processNextDrive: All drives completed, flushing buffer and emitting completion";
        flushBatchBuffer(); // 刷新剩余数据

        qDebug() << "processNextDrive: Setting state to Completed";
        m_state = ScanState::Completed;
        m_progressTimer->stop();

        qDebug() << "processNextDrive: About to update progress";
        // 避免在持有锁的情况下调用updateProgress，直接更新必要的进度信息
        m_progress.elapsedTime = m_scanTimer.elapsed();
        m_progress.storedRecords = m_totalStoredRecords;

        qDebug() << "processNextDrive: About to emit scanCompleted signal";
        emit scanCompleted(true, m_totalStoredRecords, QString());
        qDebug() << "processNextDrive: Completion signal emitted successfully";
        return;
    }

    // 获取下一个驱动器
    m_currentDrive = m_pendingDrives.takeFirst();
    m_progress.currentDrive = m_currentDrive;

    emit driveStarted(m_currentDrive);

    // 开始扫描驱动器
    uint64_t recordCount = scanSingleDrive(m_currentDrive);

    if (recordCount == 0 && !m_lastError.isEmpty()) {
        // 扫描失败
        m_state = ScanState::Error;
        m_progressTimer->stop();
        emit scanCompleted(false, m_totalStoredRecords, m_lastError);
        return;
    }

    // 更新总处理记录数和总存储记录数
    m_totalProcessedRecords += recordCount;
    m_totalStoredRecords += recordCount;

    // 发送驱动器完成信号
    emit driveCompleted(m_currentDrive, recordCount);

    qDebug() << "processNextDrive: Drive" << m_currentDrive << "completed with" << recordCount << "records";
    qDebug() << "processNextDrive: About to continue with next drive";

    // 继续处理下一个驱动器
    processNextDrive();
}

USNJournalScannerDuck::ChunkedScanResult USNJournalScannerDuck::performChunkedUSNScan(
    const QString &driveLetter, const QVector<int> &chunkSizes) {

    qDebug() << "Starting chunked USN scan for drive:" << driveLetter;
    qDebug() << "Chunk strategy: Progressive chunks with proven safe limits";
    qDebug() << "Chunk sizes:" << chunkSizes;

    ChunkedScanResult result;
    result.totalChunks = chunkSizes.size();

    QElapsedTimer totalTimer;
    totalTimer.start();

    for (int i = 0; i < chunkSizes.size(); ++i) {
        int chunkSize = chunkSizes[i];
        qDebug() << QString("=== Processing chunk %1/%2 (size: %3) ===")
                    .arg(i + 1).arg(chunkSizes.size()).arg(chunkSize);

        // 创建独立的扫描器实例，避免状态冲突
        auto chunkScanner = std::make_unique<USNJournalScannerDuck>(m_repository);

        // 为大规模扫描优化的配置
        ScanConfig config = m_config; // 继承当前配置
        config.maxRecords = chunkSize; // 设置块大小
        config.batchSize = std::max(500, chunkSize / 20); // 大批次，提高数据库效率
        config.progressReportInterval = 5000; // 减少进度报告频率
        config.testMode = false; // 真实数据库操作
        chunkScanner->setScanConfig(config);

        qDebug() << "Chunk configuration:";
        qDebug() << "  Max records:" << config.maxRecords;
        qDebug() << "  Batch size:" << config.batchSize;
        qDebug() << "  Expected time:" << (chunkSize / 2500.0) << "seconds (based on proven performance)";

        // 设置信号监听变量
        bool scanStarted = false;
        bool scanCompleted = false;
        bool scanSuccess = false;
        uint64_t chunkRecords = 0;
        QString errorMessage;
        int databaseStoreCount = 0;
        int progressCount = 0;

        // 连接信号监听
        QObject::connect(chunkScanner.get(), &USNJournalScannerDuck::scanStarted,
                         [&scanStarted](const QStringList &) {
                             scanStarted = true;
                         });

        QObject::connect(chunkScanner.get(), &USNJournalScannerDuck::scanCompleted,
                         [&](bool success, uint64_t totalRecords, const QString &error) {
                             scanCompleted = true;
                             scanSuccess = success;
                             chunkRecords = totalRecords;
                             errorMessage = error;
                         });

        QObject::connect(chunkScanner.get(), &USNJournalScannerDuck::databaseStoreProgress,
                         [&databaseStoreCount](uint64_t, uint64_t) {
                             databaseStoreCount++;
                         });

        QObject::connect(chunkScanner.get(), &USNJournalScannerDuck::scanProgress,
                         [&progressCount, chunkSize](const USNJournalScannerDuck::ScanProgress &progress) {
                             progressCount++;
                             static QElapsedTimer progressTimer;
                             if (!progressTimer.isValid()) {
                                 progressTimer.start();
                             }

                             if (progressTimer.elapsed() >= 10000) { // 每10秒显示一次
                                 qDebug() << QString("  Chunk progress: %1% (%2/%3 records, %4 stored)")
                                             .arg(progress.progressPercentage, 0, 'f', 1)
                                             .arg(progress.processedRecords)
                                             .arg(chunkSize)
                                             .arg(progress.storedRecords);
                                 progressTimer.restart();
                             }
                         });

        qDebug() << QString("Starting chunk %1 scan for drive: %2").arg(i + 1).arg(driveLetter);

        QElapsedTimer chunkTimer;
        chunkTimer.start();

        // 开始扫描这个块
        bool startSuccess = chunkScanner->startScan(driveLetter);

        if (!startSuccess) {
            qDebug() << QString("Failed to start chunk %1 scan").arg(i + 1);
            qDebug() << "This may indicate USN journal limitations or system constraints";
            continue; // 跳过这个块，继续下一个
        }

        // 等待这个块完成，使用基于块大小的动态超时
        int baseTimeoutMs = 60000; // 基础1分钟
        int dynamicTimeoutMs = baseTimeoutMs + (chunkSize / 1000 * 1000); // 每1000记录增加1秒
        int maxTimeoutMs = 600000; // 最大10分钟
        int timeoutMs = std::min(dynamicTimeoutMs, maxTimeoutMs);

        qDebug() << QString("Waiting for chunk %1 to complete (timeout: %2 seconds)...")
                    .arg(i + 1).arg(timeoutMs / 1000);

        // 等待扫描开始
        QElapsedTimer waitTimer;
        waitTimer.start();
        while (!scanStarted && waitTimer.elapsed() < 10000) {
            QCoreApplication::processEvents();
            QThread::msleep(10);
        }

        if (!scanStarted) {
            qDebug() << QString("Chunk %1 failed to start").arg(i + 1);
            continue;
        }

        // 等待扫描完成
        waitTimer.restart();
        while (!scanCompleted && waitTimer.elapsed() < timeoutMs) {
            QCoreApplication::processEvents();
            QThread::msleep(50);
        }

        if (!scanCompleted) {
            qDebug() << QString("Chunk %1 timed out after %2 seconds").arg(i + 1).arg(timeoutMs / 1000);
            qDebug() << "This is expected for very large chunks - the scan may still be processing";
            chunkScanner->stopScan(); // 停止当前扫描
            continue;
        }

        qint64 chunkElapsed = chunkTimer.elapsed();

        qDebug() << QString("=== Chunk %1 completed ===").arg(i + 1);
        qDebug() << "  Target size:" << chunkSize;
        qDebug() << "  Actual records:" << chunkRecords;
        qDebug() << "  Success:" << scanSuccess;
        qDebug() << "  Time:" << chunkElapsed << "ms (" << (chunkElapsed / 1000.0) << "seconds)";
        qDebug() << "  Database signals:" << databaseStoreCount;
        qDebug() << "  Progress signals:" << progressCount;

        if (scanSuccess) {
            result.successfulChunks++;
            result.totalRecordsScanned += chunkRecords;
            if (databaseStoreCount > 0) {
                result.totalRecordsStored += chunkRecords;
            }

            // 计算这个块的性能
            if (chunkElapsed > 0) {
                double chunkPerformance = (static_cast<double>(chunkRecords) * 1000.0) / chunkElapsed;
                qDebug() << "  Performance:" << chunkPerformance << "records/second";

                // 估算剩余时间
                if (i < chunkSizes.size() - 1) {
                    int remainingRecords = 0;
                    for (int j = i + 1; j < chunkSizes.size(); ++j) {
                        remainingRecords += chunkSizes[j];
                    }
                    double estimatedRemainingTime = remainingRecords / chunkPerformance;
                    qDebug() << "  Estimated remaining time:" << estimatedRemainingTime << "seconds";
                }
            }
        } else {
            qDebug() << "  Error:" << errorMessage;
            qDebug() << "  This may be expected if USN journal has fewer records than requested";
            if (result.errorMessage.isEmpty()) {
                result.errorMessage = errorMessage;
            }
        }

        // 清理当前扫描器并短暂休息
        chunkScanner->stopScan();
        chunkScanner.reset();
        QThread::msleep(500); // 较长休息时间，让系统稳定
    }

    result.totalElapsedMs = totalTimer.elapsed();

    // 计算平均性能
    if (result.totalElapsedMs > 0 && result.totalRecordsScanned > 0) {
        result.averagePerformance = (static_cast<double>(result.totalRecordsScanned) * 1000.0) / result.totalElapsedMs;
    }

    qDebug() << "=== Chunked USN Scan Summary ===";
    qDebug() << "  Total chunks attempted:" << result.totalChunks;
    qDebug() << "  Successful chunks:" << result.successfulChunks;
    qDebug() << "  Total records scanned:" << result.totalRecordsScanned;
    qDebug() << "  Total records stored:" << result.totalRecordsStored;
    qDebug() << "  Total time:" << result.totalElapsedMs << "ms (" << (result.totalElapsedMs / 1000.0) << "seconds)";
    qDebug() << "  Average performance:" << result.averagePerformance << "records/second";

    return result;
}
