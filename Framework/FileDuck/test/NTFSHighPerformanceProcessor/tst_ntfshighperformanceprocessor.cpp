#include <QtTest>
#include <QCoreApplication>
#include <QSignalSpy>
#include <QEventLoop>
#include <QTimer>
#include <QDebug>
#include <QTemporaryDir>
#include <QFile>
#include <thread>
#include <chrono>

#include "NTFSHighPerformanceProcessor.h"
#include "NTFSColumnBuffer.h"
#include "FileSystemRepositoryDuck.h"
#include "DatabaseManagerDuck.h"
#include "WindowsNTFSManagerDuck.h"
#include "FileSystemInfoDuck.h"

class NTFSHighPerformanceProcessorTest : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 基础功能测试
    void testProcessorCreation();
    void testConfiguration();
    void testColumnBufferCreation();
    void testColumnBufferAddRecords();
    void testColumnBufferFlush();
    
    // 高性能处理测试
    void testHighPerformanceProcessing();
    void testSIMDProcessing();
    void testProducerConsumerPattern();
    
    // 性能测试
    void testPerformanceComparison();

private:
    std::unique_ptr<DatabaseManagerDuck> m_dbManager;
    std::unique_ptr<FileSystemRepositoryDuck> m_repository;
    std::unique_ptr<NTFSHighPerformanceProcessor> m_processor;
    std::unique_ptr<WindowsNTFSManagerDuck> m_ntfsManager;
    QString m_testDbPath;
    QString m_testDrive;

    // 辅助方法
    QString getFirstAvailableNTFSDrive();
    bool isWindowsPlatform();
    void waitForDatabaseInitialization();
    std::vector<FileSystemInfoDuck> createTestFileInfos(int count);
};

void NTFSHighPerformanceProcessorTest::initTestCase()
{
    qDebug() << "=== NTFSHighPerformanceProcessor Test Suite ===";

    if (!isWindowsPlatform()) {
        QSKIP("NTFS tests are only supported on Windows platform");
    }

    // 获取测试驱动器
    m_testDrive = getFirstAvailableNTFSDrive();
    if (m_testDrive.isEmpty()) {
        QSKIP("No NTFS drive available for testing");
    }

    // 创建临时数据库
    QTemporaryDir tempDir;
    QVERIFY(tempDir.isValid());
    m_testDbPath = tempDir.path() + "/test_ntfs_high_performance.duckdb";

    qDebug() << "Using test drive:" << m_testDrive;
    qDebug() << "Test database path:" << m_testDbPath;
    qDebug() << "Test initialization completed successfully";
}

void NTFSHighPerformanceProcessorTest::cleanupTestCase()
{
    // 清理测试数据库文件
    if (QFile::exists(m_testDbPath)) {
        QFile::remove(m_testDbPath);
    }

    qDebug() << "=== NTFSHighPerformanceProcessor Test Suite Completed ===";
}

void NTFSHighPerformanceProcessorTest::init()
{
    // 每个测试前创建新的数据库管理器和仓库
    m_dbManager = std::make_unique<DatabaseManagerDuck>();
    QVERIFY(m_dbManager != nullptr);

    // 初始化数据库
    bool initSuccess = m_dbManager->initialize(m_testDbPath, 5);
    QVERIFY(initSuccess);

    // 等待数据库初始化完成
    waitForDatabaseInitialization();

    // 创建文件系统仓库
    m_repository = std::make_unique<FileSystemRepositoryDuck>(m_dbManager.get());
    QVERIFY(m_repository != nullptr);

    // 启动仓库
    m_repository->start();

    qDebug() << "Test setup completed";
}

void NTFSHighPerformanceProcessorTest::cleanup()
{
    // 每个测试后的清理
    if (m_processor && m_processor->isProcessing()) {
        m_processor->stopProcessing();
    }
    m_processor.reset();

    if (m_repository) {
        m_repository->stop();
        m_repository.reset();
    }

    m_dbManager.reset();

    qDebug() << "Test cleanup completed";
}

void NTFSHighPerformanceProcessorTest::testProcessorCreation()
{
    qDebug() << "Testing processor creation...";
    
    // 创建高性能处理器
    m_processor = std::make_unique<NTFSHighPerformanceProcessor>(m_repository.get());
    QVERIFY(m_processor != nullptr);
    QVERIFY(!m_processor->isProcessing());
    
    // 检查默认配置
    auto stats = m_processor->getPerformanceStats();
    QCOMPARE(stats.totalProduced, 0ULL);
    QCOMPARE(stats.totalParsed, 0ULL);
    QCOMPARE(stats.totalStored, 0ULL);
    
    qDebug() << "Processor creation test passed";
}

void NTFSHighPerformanceProcessorTest::testConfiguration()
{
    qDebug() << "Testing processor configuration...";
    
    if (!m_processor) {
        m_processor = std::make_unique<NTFSHighPerformanceProcessor>(m_repository.get());
    }
    
    // 测试配置
    m_processor->configure(
        2,      // producerThreads
        4,      // consumerThreads
        10000,  // queueSize
        5000,   // bufferSize
        true,   // useSIMD
        2,      // dbWriteThreads
        true    // testMode
    );
    
    // 配置应该成功（没有异常抛出）
    QVERIFY(true);
    
    qDebug() << "Configuration test passed";
}

void NTFSHighPerformanceProcessorTest::testColumnBufferCreation()
{
    qDebug() << "Testing column buffer creation...";
    
    // 创建列缓冲区
    auto columnBuffer = std::make_unique<NTFSColumnBuffer>(m_repository.get(), 1000, 2, true);
    QVERIFY(columnBuffer != nullptr);
    
    // 检查初始状态
    auto stats = columnBuffer->getStats();
    QCOMPARE(stats.totalAdded, 0ULL);
    QCOMPARE(stats.totalFlushed, 0ULL);
    QCOMPARE(stats.currentSize, 0ULL);
    
    qDebug() << "Column buffer creation test passed";
}

void NTFSHighPerformanceProcessorTest::testColumnBufferAddRecords()
{
    qDebug() << "Testing column buffer add records...";

    // 先测试创建单个FileSystemInfoDuck对象
    qDebug() << "Testing single FileSystemInfoDuck creation...";
    FileSystemInfoDuck singleInfo;
    singleInfo.setFileName("test.txt");
    singleInfo.setFilePath("C:\\test.txt");
    singleInfo.setFileSize(1024);
    singleInfo.setMftEntry(1);
    singleInfo.setIsDirectory(false);
    singleInfo.setIsDeleted(false);
    singleInfo.setDiskIdentifier("C");
    singleInfo.updateScanTime();
    qDebug() << "Single FileSystemInfoDuck created successfully";

    // 测试getter方法
    qDebug() << "Testing getter methods...";
    std::string fileName = singleInfo.getFileName();
    std::string filePath = singleInfo.getFilePath();
    qDebug() << "Getter methods work, fileName:" << QString::fromStdString(fileName);

    // 创建小量测试数据
    qDebug() << "Creating small test data...";
    std::vector<FileSystemInfoDuck> testData;
    testData.reserve(5);

    for (int i = 0; i < 5; ++i) {
        qDebug() << "Creating FileSystemInfoDuck object" << i;
        FileSystemInfoDuck fileInfo;

        qDebug() << "Setting fileName for object" << i;
        fileInfo.setFileName(QString("test_file_%1.txt").arg(i).toStdString());

        qDebug() << "Setting filePath for object" << i;
        fileInfo.setFilePath(QString("C:\\test\\test_file_%1.txt").arg(i).toStdString());

        qDebug() << "Setting basic fields for object" << i;
        fileInfo.setFileSize(1024 * (i + 1));
        fileInfo.setMftEntry(i + 1);
        fileInfo.setIsDirectory(false);
        fileInfo.setIsDeleted(false);
        fileInfo.setDiskIdentifier("C");

        qDebug() << "Setting time fields manually for object" << i;
        fileInfo.setCreatedTime("2024-01-01T00:00:00");
        fileInfo.setModifiedTime("2024-01-01T00:00:00");
        fileInfo.setAccessedTime("2024-01-01T00:00:00");
        fileInfo.setScanTime("2024-01-01T00:00:00");

        qDebug() << "Adding object" << i << "to vector";
        testData.push_back(std::move(fileInfo));
        qDebug() << "Created test file" << i;
    }
    qDebug() << "Small test data created, count:" << testData.size();

    auto columnBuffer = std::make_unique<NTFSColumnBuffer>(m_repository.get(), 10000, 1, true);
    qDebug() << "Column buffer created successfully";

    // 添加记录
    qDebug() << "Adding records to buffer...";
    bool success = columnBuffer->addRecords(testData);
    qDebug() << "Add records result:" << success;
    QVERIFY(success);

    // 检查统计信息
    qDebug() << "Checking statistics...";
    auto stats = columnBuffer->getStats();
    qDebug() << "Stats - totalAdded:" << stats.totalAdded << "currentSize:" << stats.currentSize;
    QCOMPARE(stats.totalAdded, 5ULL);
    QCOMPARE(stats.currentSize, 5ULL);

    qDebug() << "Column buffer add records test passed";
}

void NTFSHighPerformanceProcessorTest::testColumnBufferFlush()
{
    qDebug() << "Testing column buffer flush...";
    
    auto columnBuffer = std::make_unique<NTFSColumnBuffer>(m_repository.get(), 100, 2, true);
    
    // 创建测试数据
    std::vector<FileSystemInfoDuck> testData = createTestFileInfos(50);
    
    // 添加记录
    columnBuffer->addRecords(testData);
    
    // 设置信号监听
    QSignalSpy flushStartedSpy(columnBuffer.get(), &NTFSColumnBuffer::flushStarted);
    QSignalSpy flushCompletedSpy(columnBuffer.get(), &NTFSColumnBuffer::flushCompleted);
    
    // 强制刷新
    bool success = columnBuffer->flush(false); // 同步刷新
    QVERIFY(success);
    
    // 检查信号
    QCOMPARE(flushStartedSpy.count(), 1);
    QCOMPARE(flushCompletedSpy.count(), 1);
    
    // 检查统计信息
    auto stats = columnBuffer->getStats();
    QCOMPARE(stats.totalFlushed, 50ULL);
    QCOMPARE(stats.flushCount, 1ULL);
    
    qDebug() << "Column buffer flush test passed";
}

void NTFSHighPerformanceProcessorTest::testHighPerformanceProcessing()
{
    qDebug() << "Testing high-performance processing...";
    
    if (!m_processor) {
        m_processor = std::make_unique<NTFSHighPerformanceProcessor>(m_repository.get());
    }
    
    // 创建NTFS管理器
    m_ntfsManager = std::make_unique<WindowsNTFSManagerDuck>();

    // 打开卷
    bool openSuccess = m_ntfsManager->openVolume(m_testDrive.toStdString());
    QVERIFY2(openSuccess, "Failed to open volume for high-performance processing test");

    // 验证是NTFS
    bool isNTFS = m_ntfsManager->isNTFS();
    QVERIFY2(isNTFS, "Volume is not NTFS");

    qDebug() << "Volume opened successfully for high-performance processing test";

    // 配置为测试模式
    m_processor->configure(
        1,      // producerThreads
        2,      // consumerThreads
        1000,   // queueSize
        500,    // bufferSize
        true,   // useSIMD
        1,      // dbWriteThreads
        true    // testMode (跳过实际数据库操作)
    );
    
    // 设置信号监听
    QSignalSpy startedSpy(m_processor.get(), &NTFSHighPerformanceProcessor::processingStarted);
    QSignalSpy completedSpy(m_processor.get(), &NTFSHighPerformanceProcessor::processingCompleted);
    QSignalSpy progressSpy(m_processor.get(), &NTFSHighPerformanceProcessor::progressUpdated);
    
    // 开始处理（测试模式，限制条目数）
    bool success = m_processor->startProcessing(
        m_ntfsManager.get(),
        0,      // startEntry
        100,    // maxEntries (小数量用于测试)
        m_testDrive.toStdString()
    );
    
    if (success) {
        // 等待处理完成
        bool completed = completedSpy.wait(30000); // 30秒超时
        QVERIFY2(completed, "High-performance processing timeout");
        
        // 检查信号
        QCOMPARE(startedSpy.count(), 1);
        QCOMPARE(completedSpy.count(), 1);
        
        // 检查性能统计
        auto stats = m_processor->getPerformanceStats();
        qDebug() << "Performance stats:"
                 << "Produced:" << stats.totalProduced
                 << "Parsed:" << stats.totalParsed
                 << "Stored:" << stats.totalStored;

        QVERIFY(stats.totalProduced > 0);
    } else {
        qDebug() << "High-performance processing failed to start (may be expected on some systems)";
    }
    
    qDebug() << "High-performance processing test completed";
}

void NTFSHighPerformanceProcessorTest::testSIMDProcessing()
{
    qDebug() << "Testing SIMD processing...";

    if (!m_processor) {
        m_processor = std::make_unique<NTFSHighPerformanceProcessor>(m_repository.get());
    }

    // 测试SIMD启用和禁用
    m_processor->configure(1, 2, 1000, 500, true, 1, true);  // SIMD启用
    QVERIFY(true); // 配置成功

    m_processor->configure(1, 2, 1000, 500, false, 1, true); // SIMD禁用
    QVERIFY(true); // 配置成功

    qDebug() << "SIMD processing test passed";
}

void NTFSHighPerformanceProcessorTest::testProducerConsumerPattern()
{
    qDebug() << "Testing producer-consumer pattern...";

    if (!m_processor) {
        m_processor = std::make_unique<NTFSHighPerformanceProcessor>(m_repository.get());
    }

    // 测试不同的生产者-消费者配置
    m_processor->configure(1, 1, 1000, 500, true, 1, true);   // 1:1
    QVERIFY(true);

    m_processor->configure(2, 4, 1000, 500, true, 1, true);   // 1:2
    QVERIFY(true);

    m_processor->configure(4, 8, 1000, 500, true, 1, true);   // 1:2
    QVERIFY(true);

    qDebug() << "Producer-consumer pattern test passed";
}

void NTFSHighPerformanceProcessorTest::testPerformanceComparison()
{
    qDebug() << "Testing performance comparison...";

    // 这个测试主要验证高性能模式能够正常工作
    // 实际的性能比较需要在真实环境中进行

    if (!m_processor) {
        m_processor = std::make_unique<NTFSHighPerformanceProcessor>(m_repository.get());
    }

    // 配置高性能模式
    m_processor->configure(2, 4, 10000, 5000, true, 2, true);

    // 验证配置成功
    QVERIFY(!m_processor->isProcessing());

    qDebug() << "Performance comparison test passed";
}

QString NTFSHighPerformanceProcessorTest::getFirstAvailableNTFSDrive()
{
    QStringList drives = {"C", "D", "E", "F"};

    for (const QString& drive : drives) {
        auto ntfsManager = std::make_unique<WindowsNTFSManagerDuck>();
        if (ntfsManager->openVolume(drive.toStdString()) && ntfsManager->isNTFS()) {
            return drive;
        }
    }

    return QString();
}

bool NTFSHighPerformanceProcessorTest::isWindowsPlatform()
{
#ifdef _WIN32
    return true;
#else
    return false;
#endif
}

void NTFSHighPerformanceProcessorTest::waitForDatabaseInitialization()
{
    QSignalSpy spy(m_dbManager.get(), &DatabaseManagerDuck::tablesCreated);

    // 创建数据表
    m_dbManager->createTables(m_testDbPath);

    // 等待表创建完成信号
    if (spy.count() == 0) {
        QVERIFY(spy.wait(10000)); // 等待最多10秒
    }

    // 检查信号参数
    QCOMPARE(spy.count(), 1);
    QList<QVariant> arguments = spy.takeFirst();
    bool success = arguments.at(0).toBool();
    QVERIFY(success);

    qDebug() << "Database tables created successfully";
}

std::vector<FileSystemInfoDuck> NTFSHighPerformanceProcessorTest::createTestFileInfos(int count)
{
    std::vector<FileSystemInfoDuck> fileInfos;
    fileInfos.reserve(count);

    for (int i = 0; i < count; ++i) {
        FileSystemInfoDuck fileInfo;
        fileInfo.setFileName(QString("test_file_%1.txt").arg(i).toStdString());
        fileInfo.setFilePath(QString("C:\\test\\test_file_%1.txt").arg(i).toStdString());
        fileInfo.setFileSize(1024 * (i + 1));
        fileInfo.setMftEntry(i + 1);
        fileInfo.setIsDirectory(false);
        fileInfo.setIsDeleted(false);
        fileInfo.setDiskIdentifier("C");
        fileInfo.updateScanTime();

        fileInfos.push_back(std::move(fileInfo));
    }

    return fileInfos;
}

QTEST_MAIN(NTFSHighPerformanceProcessorTest)

#include "tst_ntfshighperformanceprocessor.moc"
