#include "NTFSFileScannerDuck.h"
#include "WindowsNTFSManagerDuck.h"
#include "FileSystemRepositoryDuck.h"
#include "FileSystemInfoDuck.h"
#include "NTFSHighPerformanceProcessor.h"
#include "DatabaseManagerDuck.h"
#include <QDebug>
#include <QMutexLocker>
#include <QCoreApplication>
#include <QThread>
#include <algorithm>
#include <atomic>
#include <memory>
#include <thread>
#include <chrono>

NTFSFileScannerDuck::NTFSFileScannerDuck(FileSystemRepositoryDuck *repository, QObject *parent)
    : QObject(parent), m_repository(repository), m_isScanning(false), m_isPaused(false), m_shouldStop(false),
      m_activeWorkers(0), m_totalProcessed(0), m_totalMFTSize(0) {
    
    if (!m_repository) {
        qWarning() << "FileSystemRepositoryDuck is null in NTFSFileScannerDuck constructor";
        return;
    }
    
    // 创建NTFS管理器
    m_ntfsManager = std::make_unique<WindowsNTFSManagerDuck>(this);
    
    // 连接NTFS管理器信号
    connect(m_ntfsManager.get(), &WindowsNTFSManagerDuck::scanProgress,
            this, &NTFSFileScannerDuck::onNTFSManagerProgress);
    connect(m_ntfsManager.get(), &WindowsNTFSManagerDuck::scanCompleted,
            this, &NTFSFileScannerDuck::onNTFSManagerCompleted);
    
    // 创建进度报告定时器
    m_progressTimer = new QTimer(this);
    connect(m_progressTimer, &QTimer::timeout, this, &NTFSFileScannerDuck::onProgressReportTimer);

    // 创建高性能处理器
    m_highPerfProcessor = std::make_unique<NTFSHighPerformanceProcessor>(m_repository, this);

    // 连接高性能处理器信号
    connect(m_highPerfProcessor.get(), &NTFSHighPerformanceProcessor::processingStarted,
            this, [this]() {
                qDebug() << "High-performance NTFS processing started";
            });

    connect(m_highPerfProcessor.get(), &NTFSHighPerformanceProcessor::processingCompleted,
            this, [this](uint64_t totalRecords, uint64_t elapsedMs) {
                qDebug() << "High-performance NTFS processing completed:"
                         << totalRecords << "records in" << elapsedMs << "ms";
                emit scanCompleted(true, m_currentDrive, totalRecords, elapsedMs, QString());
            });

    connect(m_highPerfProcessor.get(), &NTFSHighPerformanceProcessor::progressUpdated,
            this, [this](uint64_t processed, uint64_t total, double rate) {
                m_progress.processedFiles = processed;
                m_progress.totalFiles = total;
                m_progress.currentFile = QString("Processing at %1 records/sec").arg(rate, 0, 'f', 1);
                emit scanProgress(m_progress);
            });

    connect(m_highPerfProcessor.get(), &NTFSHighPerformanceProcessor::errorOccurred,
            this, [this](const QString& errorMessage) {
                qWarning() << "High-performance processor error:" << errorMessage;
                emit scanCompleted(false, m_currentDrive, 0, 0, errorMessage);
            });

    qDebug() << "NTFSFileScannerDuck created successfully";
}

NTFSFileScannerDuck::~NTFSFileScannerDuck() {
    stopScan();
    cleanupWorkerThreads();
    qDebug() << "NTFSFileScannerDuck destroyed";
}

void NTFSFileScannerDuck::setScanConfig(const ScanConfig &config) {
    QMutexLocker locker(&m_mutex);
    m_config = config;
    
    // 更新进度报告定时器间隔
    if (m_config.enableProgressReporting) {
        m_progressTimer->setInterval(m_config.progressReportInterval);
    }
    
    qDebug() << "Scan configuration updated:"
             << "MultiThreading:" << config.enableMultiThreading
             << "WorkerThreads:" << config.workerThreadCount
             << "BatchSize:" << config.batchSize;
}

NTFSFileScannerDuck::ScanConfig NTFSFileScannerDuck::getScanConfig() const {
    QMutexLocker locker(&m_mutex);
    return m_config;
}

bool NTFSFileScannerDuck::startScan(const QString &driveLetter) {
    return startScan(QStringList() << driveLetter);
}

bool NTFSFileScannerDuck::startScan(const QStringList &driveLetters) {
    {
        QMutexLocker locker(&m_mutex);

        if (m_isScanning) {
            qWarning() << "Scan is already in progress";
            return false;
        }

        if (driveLetters.isEmpty()) {
            qWarning() << "Drive letters list is empty";
            return false;
        }

        // 验证驱动器
        QStringList validDrives;
        for (const QString &drive : driveLetters) {
            if (WindowsNTFSManagerDuck::isDriveNTFS(drive.toStdString())) {
                validDrives.append(drive);
            } else {
                qWarning() << "Drive" << drive << "is not NTFS or not accessible";
            }
        }

        if (validDrives.isEmpty()) {
            qWarning() << "No valid NTFS drives found";
            return false;
        }

        // 重置扫描状态
        resetScanState();

        m_pendingDrives = validDrives;
        m_isScanning = true;
        m_shouldStop = false;

        // 启动进度报告定时器
        if (m_config.enableProgressReporting) {
            m_progressTimer->start();
        }

        // 开始扫描计时
        m_scanTimer.start();

        qDebug() << "Starting scan for drives:" << validDrives;
    } // 释放mutex

    // 处理第一个驱动器（在mutex外部调用）
    qDebug() << "About to call processNextDrive()...";
    processNextDrive();
    qDebug() << "processNextDrive() returned";

    return true;
}

void NTFSFileScannerDuck::stopScan() {
    QMutexLocker locker(&m_mutex);

    if (!m_isScanning) {
        return;
    }

    m_shouldStop = true;
    m_isScanning = false;
    m_isPaused = false;

    // 停止定时器
    m_progressTimer->stop();

    // 清空待处理队列
    m_pendingDrives.clear();
    m_currentDrive.clear();

    // 停止所有工作线程
    cleanupWorkerThreads();

    qDebug() << "Scan stopped";
    emit scanStopped();
}

void NTFSFileScannerDuck::pauseScan() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_isScanning || m_isPaused) {
        return;
    }
    
    m_isPaused = true;
    m_progressTimer->stop();
    
    qDebug() << "Scan paused";
    emit scanPaused();
}

void NTFSFileScannerDuck::resumeScan() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_isScanning || !m_isPaused) {
        return;
    }
    
    m_isPaused = false;
    
    if (m_config.enableProgressReporting) {
        m_progressTimer->start();
    }
    
    qDebug() << "Scan resumed";
    emit scanResumed();
}

bool NTFSFileScannerDuck::isScanning() const {
    return m_isScanning.load();
}

bool NTFSFileScannerDuck::isPaused() const {
    return m_isPaused.load();
}

NTFSFileScannerDuck::ScanProgress NTFSFileScannerDuck::getCurrentProgress() const {
    QMutexLocker locker(&m_mutex);
    return m_progress;
}

QString NTFSFileScannerDuck::getScanStatistics() const {
    QMutexLocker locker(&m_mutex);
    
    QString stats = QString("Scan Statistics:\n"
                           "Total Files: %1\n"
                           "Processed Files: %2\n"
                           "Total Size: %3 bytes\n"
                           "Processed Size: %4 bytes\n"
                           "Elapsed Time: %5 ms\n"
                           "Scan Speed: %6 files/sec\n"
                           "Current Drive: %7\n"
                           "Current File: %8")
                    .arg(m_progress.totalFiles)
                    .arg(m_progress.processedFiles)
                    .arg(m_progress.totalSize)
                    .arg(m_progress.processedSize)
                    .arg(m_progress.elapsedTime)
                    .arg(m_progress.scanSpeed, 0, 'f', 2)
                    .arg(m_progress.diskIdentifier)
                    .arg(m_progress.currentFile);
    
    return stats;
}

QStringList NTFSFileScannerDuck::getAvailableNTFSDrives() {
    std::vector<std::string> drives = WindowsNTFSManagerDuck::getAvailableDrives();
    QStringList driveList;
    
    for (const auto &drive : drives) {
        driveList.append(QString::fromStdString(drive));
    }
    
    return driveList;
}

int NTFSFileScannerDuck::estimateScanTime(const QString &driveLetter) {
    // 这里应该实现扫描时间估算逻辑
    // 目前返回一个基于驱动器的简单估算
    Q_UNUSED(driveLetter)
    
    // 假设每个驱动器需要60秒扫描时间
    return 60;
}

void NTFSFileScannerDuck::onProgressReportTimer() {
    if (!m_isScanning || m_isPaused) {
        return;
    }
    
    // 更新进度信息
    {
        QMutexLocker locker(&m_mutex);
        m_progress.elapsedTime = m_scanTimer.elapsed();
        
        if (m_progress.elapsedTime > 0) {
            m_progress.scanSpeed = static_cast<double>(m_progress.processedFiles) / 
                                  (static_cast<double>(m_progress.elapsedTime) / 1000.0);
        }
    }
    
    emit scanProgress(m_progress);
}

void NTFSFileScannerDuck::onNTFSManagerProgress(uint64_t processed, uint64_t total, const QString &currentFile) {
    updateProgress(processed, total, currentFile);
}

void NTFSFileScannerDuck::onNTFSManagerCompleted(bool success, uint64_t scannedCount, const QString &errorMessage) {
    QMutexLocker locker(&m_mutex);
    
    qint64 elapsedTime = m_scanTimer.elapsed();
    
    if (success) {
        qDebug() << "NTFS scan completed for drive" << m_currentDrive 
                 << "- scanned" << scannedCount << "files in" << elapsedTime << "ms";
    } else {
        qWarning() << "NTFS scan failed for drive" << m_currentDrive << ":" << errorMessage;
    }
    
    emit scanCompleted(success, m_currentDrive, scannedCount, elapsedTime, errorMessage);
    
    // 处理下一个驱动器
    processNextDrive();
}

bool NTFSFileScannerDuck::performSingleDiskScan(const QString &driveLetter) {
    if (!m_ntfsManager) {
        qWarning() << "NTFS manager is null";
        return false;
    }

    // 检查是否启用高性能模式
    if (m_config.enableHighPerformanceMode) {
        qDebug() << "Using high-performance mode for drive:" << driveLetter;
        uint64_t scannedCount = performHighPerformanceScan(driveLetter);
        return scannedCount > 0;
    }

    // 检查是否启用多线程
    if (m_config.enableMultiThreading && m_config.workerThreadCount > 1) {
        // 获取MFT大小
        uint64_t mftSize = getMFTSize(driveLetter);
        if (mftSize > 0) {
            qDebug() << "Using multi-threaded MFT segment scanning for drive:" << driveLetter
                     << "MFT size:" << mftSize << "threads:" << m_config.workerThreadCount;

            // 使用MFT分段并行扫描
            createMFTSegmentWorkers(driveLetter, mftSize);
            // 不等待工作线程，让它们异步完成
            return true;
        } else {
            qWarning() << "Failed to get MFT size, falling back to single-threaded scanning";
        }
    }

    // 单线程扫描（原有逻辑）
    qDebug() << "Using single-threaded scanning for drive:" << driveLetter;

    // 打开卷
    if (!m_ntfsManager->openVolume(driveLetter.toStdString())) {
        qWarning() << "Failed to open volume:" << driveLetter;
        return false;
    }

    // 开始扫描
    std::vector<FileSystemInfoDuck> fileInfos;

    auto progressCallback = [this](uint64_t processed, uint64_t total, const std::string &currentFile) {
        updateProgress(processed, total, QString::fromStdString(currentFile));
    };

    uint64_t scannedCount;

    // 应用测试模式限制
    if (m_config.testMode && m_config.testModeMaxEntries > 0) {
        uint64_t mftSize = m_ntfsManager->getMFTSize();
        uint64_t effectiveSize = std::min(mftSize, m_config.testModeMaxEntries);
        qDebug() << "Test mode enabled: limiting single-threaded scan to" << effectiveSize << "entries (original:" << mftSize << ")";
        scannedCount = m_ntfsManager->scanMFTSegment(0, effectiveSize, fileInfos, progressCallback);
    } else {
        scannedCount = m_ntfsManager->scanMFTToFileInfos(fileInfos, progressCallback);
    }

    if (scannedCount == 0) {
        qWarning() << "No files scanned from drive:" << driveLetter;
        return false;
    }

    // 存储到数据库
    if (m_config.enableDatabaseStorage && m_repository) {
        int storedCount = storeFileInfosToDatabase(fileInfos, driveLetter);
        qDebug() << "Stored" << storedCount << "file records to database";
    }

    return true;
}

int NTFSFileScannerDuck::storeFileInfosToDatabase(const std::vector<FileSystemInfoDuck> &fileInfos, const QString &driveLetter) {
    if (!m_repository || fileInfos.empty()) {
        return 0;
    }

    // 清除现有数据（如果配置要求）
    if (m_config.clearExistingData) {
        // 这里应该调用清除数据的方法
        qDebug() << "Clearing existing data for drive:" << driveLetter;
    }

    // 转换为QList
    QList<FileSystemInfoDuck> fileInfoList;
    for (const auto &fileInfo : fileInfos) {
        fileInfoList.append(fileInfo);
    }

    // 批量插入到数据库
    int totalStored = 0;
    int batchSize = m_config.batchSize;

    for (int i = 0; i < fileInfoList.size(); i += batchSize) {
        if (!shouldContinueScanning()) {
            break;
        }

        int endIndex = std::min(i + batchSize, static_cast<int>(fileInfoList.size()));
        QList<FileSystemInfoDuck> batch = fileInfoList.mid(i, endIndex - i);

        // 同步批量插入
        bool insertSuccess = false;
        int insertedCount = 0;

        // 使用回调等待插入完成
        QEventLoop loop;
        QString batchError;
        m_repository->batchInsert(batch, [&](const AsyncResultDuck<int> &result) {
            insertSuccess = result.success;
            insertedCount = result.data;
            batchError = result.error;
            loop.quit();
        });
        loop.exec();

        if (insertSuccess) {
            totalStored += insertedCount;
            // 只在每100个批次输出一次成功日志
            if ((i / batchSize + 1) % 100 == 0) {
                qDebug() << "Successfully inserted batch" << (i / batchSize + 1) << ":" << insertedCount << "records";
            }
            emit databaseStorageProgress(totalStored, fileInfoList.size());
        } else {
            qWarning() << "Failed to insert batch starting at index" << i << "Error:" << batchError;
        }
    }

    emit databaseStorageCompleted(totalStored > 0, totalStored,
                                 totalStored > 0 ? QString() : "Database storage failed");

    return totalStored;
}

int NTFSFileScannerDuck::storeFileInfosToDatabaseThreadSafe(const std::vector<FileSystemInfoDuck> &fileInfos, const QString &driveLetter, int workerId) {
    if (!m_repository || fileInfos.empty()) {
        return 0;
    }

    // 检查是否应该继续扫描
    if (!shouldContinueScanning()) {
        qDebug() << "Worker" << workerId << "skipping database storage due to scan cancellation";
        return 0;
    }

    // 使用静态互斥锁确保数据库操作的线程安全
    static QMutex s_dbMutex;
    static bool s_dataCleared = false;

    QMutexLocker locker(&s_dbMutex);

    // 只有第一个线程清除数据
    if (m_config.clearExistingData && !s_dataCleared) {
        qDebug() << "Worker" << workerId << "clearing existing data for drive:" << driveLetter;
        s_dataCleared = true;
        // 这里可以添加清除数据的逻辑
    }

    // 转换为QList
    QList<FileSystemInfoDuck> fileInfoList;
    fileInfoList.reserve(fileInfos.size());
    for (const auto &fileInfo : fileInfos) {
        fileInfoList.append(fileInfo);
    }

    // 使用较小的批次大小减少数据库压力
    int totalStored = 0;
    int batchSize = std::min(m_config.batchSize, 1000); // 限制最大批次大小

    for (int i = 0; i < fileInfoList.size(); i += batchSize) {
        // 在每个批次之前检查是否应该继续
        if (!shouldContinueScanning()) {
            qDebug() << "Worker" << workerId << "stopping database storage due to scan cancellation";
            break;
        }

        int endIndex = std::min(i + batchSize, static_cast<int>(fileInfoList.size()));
        QList<FileSystemInfoDuck> batch = fileInfoList.mid(i, endIndex - i);

        // 使用线程安全的同步插入方式
        bool insertSuccess = false;
        int insertedCount = 0;

        try {
            // 使用信号量或条件变量进行同步，避免QEventLoop
            std::atomic<bool> operationCompleted{false};
            std::atomic<bool> operationSuccess{false};
            std::atomic<int> operationResult{0};

            m_repository->batchInsert(batch, [&](const AsyncResultDuck<int> &result) {
                operationSuccess = result.success;
                operationResult = result.data;
                operationCompleted = true;
            });

            // 使用简单的轮询等待，避免Qt事件循环
            int waitCount = 0;
            const int maxWaitMs = 5000; // 5秒超时
            const int pollIntervalMs = 10;

            while (!operationCompleted && waitCount < (maxWaitMs / pollIntervalMs)) {
                std::this_thread::sleep_for(std::chrono::milliseconds(pollIntervalMs));
                waitCount++;
            }

            if (operationCompleted) {
                insertSuccess = operationSuccess;
                insertedCount = operationResult;

                if (insertSuccess) {
                    totalStored += insertedCount;
                    // 只在每100个批次输出一次成功日志
                    if ((i/batchSize + 1) % 100 == 0) {
                        qDebug() << "Worker" << workerId << "successfully inserted batch" << (i/batchSize + 1)
                                 << "with" << insertedCount << "records";
                    }
                } else {
                    qWarning() << "Worker" << workerId << "failed to insert batch starting at index" << i;
                }
            } else {
                qWarning() << "Worker" << workerId << "batch insert timeout after" << maxWaitMs << "ms";
            }
        } catch (const std::exception &e) {
            qWarning() << "Worker" << workerId << "batch insert exception:" << e.what();
        } catch (...) {
            qWarning() << "Worker" << workerId << "batch insert unknown exception";
        }
    }

    if (totalStored > 0) {
        qDebug() << "Worker" << workerId << "total stored:" << totalStored << "out of" << fileInfos.size();
    }

    return totalStored;
}

void NTFSFileScannerDuck::updateProgress(uint64_t processed, uint64_t total, const QString &currentFile) {
    QMutexLocker locker(&m_mutex);

    m_progress.processedFiles = processed;
    m_progress.totalFiles = total;
    m_progress.currentFile = currentFile;
    m_progress.diskIdentifier = m_currentDrive;
    m_progress.elapsedTime = m_scanTimer.elapsed();

    if (m_progress.elapsedTime > 0) {
        m_progress.scanSpeed = static_cast<double>(processed) /
                              (static_cast<double>(m_progress.elapsedTime) / 1000.0);
    }
}

void NTFSFileScannerDuck::resetScanState() {
    m_progress = ScanProgress();
    m_pendingDrives.clear();
    m_currentDrive.clear();
    m_isScanning = false;
    m_isPaused = false;
    m_shouldStop = false;

    // 重置多线程相关状态
    m_activeWorkers = 0;
    m_totalProcessed = 0;
    m_totalMFTSize = 0;
    cleanupWorkerThreads();
}

bool NTFSFileScannerDuck::shouldContinueScanning() const {
    return m_isScanning.load() && !m_shouldStop.load();
}

void NTFSFileScannerDuck::processNextDrive() {
    qDebug() << "processNextDrive() called";
    QMutexLocker locker(&m_mutex);
    qDebug() << "processNextDrive() acquired mutex";

    if (m_pendingDrives.isEmpty() || m_shouldStop) {
        // 所有驱动器扫描完成
        m_isScanning = false;
        m_progressTimer->stop();

        qDebug() << "All drives scan completed";
        return;
    }

    // 获取下一个驱动器
    m_currentDrive = m_pendingDrives.takeFirst();

    qDebug() << "Starting scan for drive:" << m_currentDrive;
    qDebug() << "About to emit scanStarted signal...";
    emit scanStarted(m_currentDrive);
    qDebug() << "scanStarted signal emitted";

    // 在新线程中执行扫描
    qDebug() << "Creating worker thread...";
    QThread::create([this]() {
        qDebug() << "Worker thread started for drive:" << m_currentDrive;
        bool success = performSingleDiskScan(m_currentDrive);
        qDebug() << "performSingleDiskScan completed with success:" << success;

        if (!success) {
            QString errorMsg = QString("Failed to scan drive: %1").arg(m_currentDrive);
            emit scanCompleted(false, m_currentDrive, 0, m_scanTimer.elapsed(), errorMsg);

            // 继续处理下一个驱动器
            QMetaObject::invokeMethod(this, "processNextDrive", Qt::QueuedConnection);
        }
    })->start();
    qDebug() << "Worker thread created and started";
}

void NTFSFileScannerDuck::createMFTSegmentWorkers(const QString &driveLetter, uint64_t mftSize) {
    if (mftSize == 0) {
        qWarning() << "Invalid MFT size for drive:" << driveLetter;
        return;
    }

    // 应用测试模式限制
    uint64_t effectiveMftSize = mftSize;
    if (m_config.testMode && m_config.testModeMaxEntries > 0) {
        effectiveMftSize = std::min(mftSize, m_config.testModeMaxEntries);
        qDebug() << "Test mode enabled: limiting scan to" << effectiveMftSize << "entries (original:" << mftSize << ")";
    }

    int numThreads = m_config.workerThreadCount;
    uint64_t segmentSize = effectiveMftSize / numThreads;

    if (segmentSize < 100) {
        qDebug() << "MFT segment too small, using single thread for drive:" << driveLetter;
        numThreads = 1;
        segmentSize = effectiveMftSize;
    }

    m_totalMFTSize = effectiveMftSize;
    m_activeWorkers = numThreads;

    qDebug() << "Creating" << numThreads << "MFT segment workers for drive:" << driveLetter
             << "Effective MFT size:" << effectiveMftSize << "segment size:" << segmentSize;

    // 创建工作线程
    for (int i = 0; i < numThreads; ++i) {
        uint64_t startEntry = i * segmentSize;
        uint64_t endEntry = (i == numThreads - 1) ? effectiveMftSize : (i + 1) * segmentSize;

        QThread *workerThread = QThread::create([this, driveLetter, startEntry, endEntry, i]() {
            bool success = performMFTSegmentScan(driveLetter, startEntry, endEntry, i);

            // 减少活跃工作线程计数
            int remaining = m_activeWorkers.fetch_sub(1) - 1;

            if (remaining == 0) {
                // 所有工作线程完成
                QMetaObject::invokeMethod(this, [this, driveLetter]() {
                    qDebug() << "All MFT segment workers completed for drive:" << driveLetter;
                    emit scanCompleted(true, driveLetter, m_totalProcessed.load(), m_scanTimer.elapsed(), QString());

                    // 继续处理下一个驱动器
                    processNextDrive();
                }, Qt::QueuedConnection);
            }
        });

        m_workerThreads.append(workerThread);
        workerThread->start();
    }
}

bool NTFSFileScannerDuck::performMFTSegmentScan(const QString &driveLetter, uint64_t startEntry, uint64_t endEntry, int workerId) {
    qDebug() << "Worker" << workerId << "scanning MFT segment" << startEntry << "to" << endEntry << "for drive:" << driveLetter;

    // 检查是否应该继续扫描
    if (!shouldContinueScanning()) {
        qDebug() << "Worker" << workerId << "stopping due to scan cancellation";
        return false;
    }

    // 使用现有的NTFS管理器进行批量读取（避免重复初始化）
    std::vector<WindowsNTFSManagerDuck::MFTRecordInfo> mftRecords;

    // 创建临时的NTFS管理器用于这个工作线程
    auto ntfsManager = std::make_unique<WindowsNTFSManagerDuck>();

    // 简化的卷打开（减少重复操作）
    if (!ntfsManager->openVolume(driveLetter.toStdString())) {
        qWarning() << "Worker" << workerId << "failed to open volume:" << driveLetter;
        return false;
    }

    // 直接使用批量读取MFT记录
    uint64_t count = endEntry - startEntry;
    auto progressCallback = [this, workerId](uint64_t processed, uint64_t total, const std::string &currentFile) {
        // 更新总进度
        m_totalProcessed.fetch_add(1);

        // 定期更新进度信号（避免过于频繁）
        if (processed % 2000 == 0) {
            uint64_t totalProcessed = m_totalProcessed.load();
            updateProgress(totalProcessed, m_totalMFTSize.load(), QString::fromStdString(currentFile));
        }
    };

    uint64_t scannedCount = ntfsManager->readMFTRecordsBatch(startEntry, count, mftRecords, progressCallback);

    if (scannedCount == 0) {
        qDebug() << "Worker" << workerId << "read 0 MFT records from segment" << startEntry << "to" << endEntry;
        return true; // 空段也算成功
    }

    qDebug() << "Worker" << workerId << "read" << scannedCount << "MFT records from segment" << startEntry << "to" << endEntry;

    // 再次检查是否应该继续扫描（在数据库操作之前）
    if (!shouldContinueScanning()) {
        qDebug() << "Worker" << workerId << "stopping before database operations due to scan cancellation";
        return false;
    }

    // 转换为FileSystemInfoDuck格式（如果需要存储到数据库）
    if (m_config.enableDatabaseStorage && m_repository) {
        qDebug() << "Worker" << workerId << "converting" << mftRecords.size() << "MFT records to FileSystemInfoDuck format";

        std::vector<FileSystemInfoDuck> fileInfos;
        fileInfos.reserve(mftRecords.size());

        for (const auto &record : mftRecords) {
            // 过滤掉目录，只存储文件
            if (record.isDirectory) {
                continue;
            }

            FileSystemInfoDuck info;
            info.setFileName(record.fileName);
            info.setFilePath(record.filePath);
            info.setFileSize(record.fileSize);
            info.setMftEntry(record.mftEntry);
            info.setIsDirectory(record.isDirectory);

            // 转换时间格式（从FILETIME uint64_t到字符串）
            info.setCreatedTime(std::to_string(record.createdTime));
            info.setModifiedTime(std::to_string(record.modifiedTime));
            info.setAccessedTime(std::to_string(record.accessedTime));

            info.setDiskIdentifier(driveLetter.toStdString());
            info.updateScanTime();
            fileInfos.push_back(info);
        }

        qDebug() << "Worker" << workerId << "filtered to" << fileInfos.size() << "files (excluding directories)";

        // 最后一次检查是否应该继续扫描（在数据库存储之前）
        if (!shouldContinueScanning()) {
            qDebug() << "Worker" << workerId << "stopping before database storage due to scan cancellation";
            return false;
        }

        // 使用优化的线程安全数据库存储
        int storedCount = 0;
        QElapsedTimer dbTimer;
        dbTimer.start();

        try {
            storedCount = storeFileInfosToDatabaseThreadSafe(fileInfos, driveLetter, workerId);
            qint64 dbElapsed = dbTimer.elapsed();
            double recordsPerSecond = storedCount > 0 ? (storedCount * 1000.0 / dbElapsed) : 0;
            // 只输出工作线程完成的汇总信息
            qDebug() << "Worker" << workerId << "completed: stored" << storedCount << "records in"
                     << (dbElapsed/1000.0) << "sec (" << QString::number(recordsPerSecond, 'f', 0) << "rec/sec)";
        } catch (const std::exception &e) {
            qWarning() << "Worker" << workerId << "database storage failed:" << e.what();
        } catch (...) {
            qWarning() << "Worker" << workerId << "database storage failed with unknown error";
        }
    }

    return true;
}

uint64_t NTFSFileScannerDuck::getMFTSize(const QString &driveLetter) {
    if (!m_ntfsManager) {
        return 0;
    }

    // 打开卷
    if (!m_ntfsManager->openVolume(driveLetter.toStdString())) {
        qWarning() << "Failed to open volume for MFT size query:" << driveLetter;
        return 0;
    }

    uint64_t mftSize = m_ntfsManager->getMFTSize();
    qDebug() << "MFT size for drive" << driveLetter << ":" << mftSize;

    return mftSize;
}

void NTFSFileScannerDuck::waitForAllWorkers() {
    qDebug() << "Waiting for" << m_workerThreads.size() << "worker threads to complete...";

    for (QThread *thread : m_workerThreads) {
        if (thread && thread->isRunning()) {
            thread->wait(30000); // 最多等待30秒
        }
    }

    qDebug() << "All worker threads completed";
}

void NTFSFileScannerDuck::cleanupWorkerThreads() {
    for (QThread *thread : m_workerThreads) {
        if (thread) {
            if (thread->isRunning()) {
                thread->requestInterruption();
                thread->wait(5000); // 等待5秒
                if (thread->isRunning()) {
                    thread->terminate();
                    thread->wait(1000);
                }
            }
            thread->deleteLater();
        }
    }
    m_workerThreads.clear();
}

uint64_t NTFSFileScannerDuck::performHighPerformanceScan(const QString &driveLetter) {
    qDebug() << "Starting high-performance NTFS scan for drive:" << driveLetter;

    if (!m_highPerfProcessor) {
        qWarning() << "High performance processor not available";
        return 0;
    }

    if (!m_ntfsManager) {
        qWarning() << "NTFS manager not available";
        return 0;
    }

    // 打开卷
    if (!m_ntfsManager->openVolume(driveLetter.toStdString())) {
        qWarning() << "Failed to open volume:" << driveLetter;
        return 0;
    }

    // 获取MFT大小
    uint64_t mftSize = m_ntfsManager->getMFTSize();
    if (mftSize == 0) {
        qWarning() << "Failed to get MFT size for drive:" << driveLetter;
        return 0;
    }

    // 在测试模式下限制扫描范围
    uint64_t maxEntries = mftSize;
    if (m_config.testMode) {
        maxEntries = std::min(mftSize, m_config.testModeMaxEntries);
        qDebug() << "Test mode enabled, limiting scan to" << maxEntries << "entries";
    }

    qDebug() << "MFT size:" << mftSize << "Max entries to scan:" << maxEntries;

    // 配置高性能处理器
    m_highPerfProcessor->configure(
        m_config.producerThreads,
        m_config.consumerThreads,
        m_config.rawDataQueueSize,
        m_config.columnBufferSize,
        m_config.useSIMD,
        m_config.databaseWriteThreads,
        m_config.testMode
    );

    // 开始高性能处理
    bool success = m_highPerfProcessor->startProcessing(
        m_ntfsManager.get(),
        0, // 从第一个MFT条目开始
        maxEntries,
        driveLetter.toStdString()
    );

    if (!success) {
        qWarning() << "Failed to start high-performance processing";
        return 0;
    }

    // 等待处理完成（最多5分钟）
    bool completed = m_highPerfProcessor->waitForCompletion(300000);
    if (!completed) {
        qWarning() << "High-performance processing timed out";
        m_highPerfProcessor->stopProcessing();
        return 0;
    }

    // 获取性能统计
    auto stats = m_highPerfProcessor->getPerformanceStats();
    uint64_t totalProcessed = stats.totalStored;

    qDebug() << "High-performance scan completed:"
             << "Produced:" << stats.totalProduced
             << "Parsed:" << stats.totalParsed
             << "Stored:" << totalProcessed
             << "Parse rate:" << stats.parseRate << "records/sec"
             << "Store rate:" << stats.storeRate << "records/sec";

    return totalProcessed;
}
