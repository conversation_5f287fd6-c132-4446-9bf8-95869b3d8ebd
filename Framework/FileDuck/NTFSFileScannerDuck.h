#ifndef NTFSFILESCANNERDUCK_H
#define NTFSFILESCANNERDUCK_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QTimer>
#include <QString>
#include <QStringList>
#include <QElapsedTimer>
#include <memory>
#include <vector>
#include <atomic>
#include <functional>

// 前向声明
class WindowsNTFSManagerDuck;
class FileSystemRepositoryDuck;
class FileSystemInfoDuck;
class NTFSHighPerformanceProcessor;

/**
 * @brief NTFS文件扫描器（DuckDB版本）
 *
 * 提供NTFS文件系统的高级扫描功能，使用WindowsNTFSManagerDuck进行底层访问
 * 支持多线程扫描、进度报告、批量数据库存储等功能
 * 将扫描结果存储到DuckDB数据库中
 */
class NTFSFileScannerDuck : public QObject {
    Q_OBJECT

public:
    /**
     * @brief 扫描进度信息结构
     */
    struct ScanProgress {
        uint64_t totalFiles;        ///< 总文件数
        uint64_t processedFiles;    ///< 已处理文件数
        uint64_t totalSize;         ///< 总文件大小
        uint64_t processedSize;     ///< 已处理文件大小
        QString currentFile;        ///< 当前处理的文件
        QString diskIdentifier;     ///< 磁盘标识符
        qint64 elapsedTime;         ///< 已用时间（毫秒）
        double scanSpeed;           ///< 扫描速度（文件/秒）
        
        ScanProgress() : totalFiles(0), processedFiles(0), totalSize(0), 
                        processedSize(0), elapsedTime(0), scanSpeed(0.0) {}
    };

    /**
     * @brief 扫描配置结构
     */
    struct ScanConfig {
        bool enableMultiThreading;      ///< 启用多线程扫描
        int workerThreadCount;          ///< 工作线程数量
        int batchSize;                  ///< 批处理大小
        bool enableProgressReporting;   ///< 启用进度报告
        int progressReportInterval;     ///< 进度报告间隔（毫秒）
        bool enableDatabaseStorage;     ///< 启用数据库存储
        bool clearExistingData;         ///< 清除现有数据
        bool useMemoryDatabaseFirst;    ///< 先使用内存数据库，扫描完成后批量写入文件数据库

        // 测试模式配置
        bool testMode;                  ///< 启用测试模式（限制扫描范围）
        uint64_t testModeMaxEntries;    ///< 测试模式下最大扫描条目数

        // 高性能处理器配置
        bool enableHighPerformanceMode; ///< 启用高性能模式（生产者-消费者架构）
        int producerThreads;            ///< 生产者线程数
        int consumerThreads;            ///< 消费者线程数
        int rawDataQueueSize;           ///< 原始数据队列大小
        int columnBufferSize;           ///< 列缓冲区大小
        bool useSIMD;                   ///< 启用SIMD优化
        int databaseWriteThreads;       ///< 数据库写入线程数

        ScanConfig() : enableMultiThreading(true), workerThreadCount(4), batchSize(1000),
                      enableProgressReporting(true), progressReportInterval(1000),
                      enableDatabaseStorage(true), clearExistingData(false), useMemoryDatabaseFirst(true),
                      testMode(false), testModeMaxEntries(10000),
                      enableHighPerformanceMode(false), producerThreads(4), consumerThreads(8),
                      rawDataQueueSize(100000), columnBufferSize(50000), useSIMD(true),
                      databaseWriteThreads(4) {}
    };

    /**
     * @brief 构造函数
     * @param repository 文件系统仓库指针
     * @param parent 父对象
     */
    explicit NTFSFileScannerDuck(FileSystemRepositoryDuck *repository, QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~NTFSFileScannerDuck() override;

    /**
     * @brief 设置扫描配置
     * @param config 扫描配置
     */
    void setScanConfig(const ScanConfig &config);

    /**
     * @brief 获取扫描配置
     * @return 当前扫描配置
     */
    ScanConfig getScanConfig() const;

    /**
     * @brief 开始扫描指定磁盘
     * @param driveLetter 驱动器字母（如 "C"）
     * @return 成功启动返回true
     */
    bool startScan(const QString &driveLetter);

    /**
     * @brief 开始扫描多个磁盘
     * @param driveLetters 驱动器字母列表
     * @return 成功启动返回true
     */
    bool startScan(const QStringList &driveLetters);

    /**
     * @brief 停止扫描
     */
    void stopScan();

    /**
     * @brief 暂停扫描
     */
    void pauseScan();

    /**
     * @brief 恢复扫描
     */
    void resumeScan();

    /**
     * @brief 检查是否正在扫描
     * @return 正在扫描返回true
     */
    bool isScanning() const;

    /**
     * @brief 检查是否已暂停
     * @return 已暂停返回true
     */
    bool isPaused() const;

    /**
     * @brief 获取当前扫描进度
     * @return 扫描进度信息
     */
    ScanProgress getCurrentProgress() const;

    /**
     * @brief 获取扫描统计信息
     * @return 统计信息字符串
     */
    QString getScanStatistics() const;

    /**
     * @brief 获取可用的NTFS驱动器列表
     * @return 驱动器字母列表
     */
    static QStringList getAvailableNTFSDrives();

    /**
     * @brief 估算磁盘扫描时间
     * @param driveLetter 驱动器字母
     * @return 估算时间（秒），失败返回-1
     */
    static int estimateScanTime(const QString &driveLetter);

signals:
    /**
     * @brief 扫描开始信号
     * @param driveLetter 驱动器字母
     */
    void scanStarted(const QString &driveLetter);

    /**
     * @brief 扫描进度信号
     * @param progress 进度信息
     */
    void scanProgress(const NTFSFileScannerDuck::ScanProgress &progress);

    /**
     * @brief 扫描完成信号
     * @param success 是否成功
     * @param driveLetter 驱动器字母
     * @param scannedCount 扫描的文件数量
     * @param elapsedTime 耗时（毫秒）
     * @param errorMessage 错误信息
     */
    void scanCompleted(bool success, const QString &driveLetter, uint64_t scannedCount, 
                      qint64 elapsedTime, const QString &errorMessage);

    /**
     * @brief 扫描暂停信号
     */
    void scanPaused();

    /**
     * @brief 扫描恢复信号
     */
    void scanResumed();

    /**
     * @brief 扫描停止信号
     */
    void scanStopped();

    /**
     * @brief 数据库存储进度信号
     * @param processed 已处理数量
     * @param total 总数量
     */
    void databaseStorageProgress(int processed, int total);

    /**
     * @brief 数据库存储完成信号
     * @param success 是否成功
     * @param storedCount 存储的记录数量
     * @param errorMessage 错误信息
     */
    void databaseStorageCompleted(bool success, int storedCount, const QString &errorMessage);

private slots:
    /**
     * @brief 处理进度报告定时器
     */
    void onProgressReportTimer();

    /**
     * @brief 处理NTFS管理器扫描进度
     * @param processed 已处理数量
     * @param total 总数量
     * @param currentFile 当前文件
     */
    void onNTFSManagerProgress(uint64_t processed, uint64_t total, const QString &currentFile);

    /**
     * @brief 处理NTFS管理器扫描完成
     * @param success 是否成功
     * @param scannedCount 扫描数量
     * @param errorMessage 错误信息
     */
    void onNTFSManagerCompleted(bool success, uint64_t scannedCount, const QString &errorMessage);

private:
    FileSystemRepositoryDuck *m_repository;                    ///< 文件系统仓库
    std::unique_ptr<WindowsNTFSManagerDuck> m_ntfsManager;     ///< NTFS管理器
    std::unique_ptr<DatabaseManagerDuck> m_memoryDbManager;    ///< 内存数据库管理器
    std::unique_ptr<FileSystemRepositoryDuck> m_memoryRepository; ///< 内存数据库仓库
    ScanConfig m_config;                                        ///< 扫描配置
    ScanProgress m_progress;                                    ///< 扫描进度
    
    std::atomic<bool> m_isScanning;                            ///< 是否正在扫描
    std::atomic<bool> m_isPaused;                              ///< 是否已暂停
    std::atomic<bool> m_shouldStop;                            ///< 是否应该停止
    
    QStringList m_pendingDrives;                               ///< 待扫描驱动器列表
    QString m_currentDrive;                                    ///< 当前扫描的驱动器
    
    QTimer *m_progressTimer;                                   ///< 进度报告定时器
    QElapsedTimer m_scanTimer;                                 ///< 扫描计时器

    // 多线程扫描相关
    QList<QThread*> m_workerThreads;                          ///< 工作线程列表
    std::atomic<int> m_activeWorkers;                         ///< 活跃工作线程数
    std::atomic<uint64_t> m_totalProcessed;                   ///< 总处理文件数
    std::atomic<uint64_t> m_totalMFTSize;                     ///< 总MFT大小

    // 高性能处理器
    std::unique_ptr<NTFSHighPerformanceProcessor> m_highPerfProcessor; ///< 高性能处理器

    mutable QMutex m_mutex;                                    ///< 线程安全保护

    /**
     * @brief 执行单个磁盘扫描
     * @param driveLetter 驱动器字母
     * @return 成功返回true
     */
    bool performSingleDiskScan(const QString &driveLetter);

    /**
     * @brief 执行高性能磁盘扫描
     * @param driveLetter 驱动器字母
     * @return 扫描的记录数量
     */
    uint64_t performHighPerformanceScan(const QString &driveLetter);

    /**
     * @brief 创建MFT分段工作线程
     * @param driveLetter 驱动器字母
     * @param mftSize MFT总大小
     */
    void createMFTSegmentWorkers(const QString &driveLetter, uint64_t mftSize);

    /**
     * @brief 执行MFT分段扫描
     * @param driveLetter 驱动器字母
     * @param startEntry 起始MFT条目
     * @param endEntry 结束MFT条目
     * @param workerId 工作线程ID
     * @return 成功返回true
     */
    bool performMFTSegmentScan(const QString &driveLetter, uint64_t startEntry, uint64_t endEntry, int workerId);

    /**
     * @brief 处理扫描结果并存储到数据库
     * @param fileInfos 文件信息列表
     * @param driveLetter 驱动器字母
     * @return 存储的记录数量
     */
    int storeFileInfosToDatabase(const std::vector<FileSystemInfoDuck> &fileInfos, const QString &driveLetter);

    /**
     * @brief 线程安全的数据库存储方法
     * @param fileInfos 文件信息列表
     * @param driveLetter 驱动器字母
     * @param workerId 工作线程ID
     * @return 存储的记录数量
     */
    int storeFileInfosToDatabaseThreadSafe(const std::vector<FileSystemInfoDuck> &fileInfos, const QString &driveLetter, int workerId);

    /**
     * @brief 更新扫描进度
     * @param processed 已处理数量
     * @param total 总数量
     * @param currentFile 当前文件
     */
    void updateProgress(uint64_t processed, uint64_t total, const QString &currentFile);

    /**
     * @brief 重置扫描状态
     */
    void resetScanState();

    /**
     * @brief 获取MFT大小
     * @param driveLetter 驱动器字母
     * @return MFT大小，失败返回0
     */
    uint64_t getMFTSize(const QString &driveLetter);

    /**
     * @brief 等待所有工作线程完成
     */
    void waitForAllWorkers();

    /**
     * @brief 清理工作线程
     */
    void cleanupWorkerThreads();

    /**
     * @brief 检查是否应该继续扫描
     * @return 应该继续返回true
     */
    bool shouldContinueScanning() const;

    /**
     * @brief 处理下一个待扫描的驱动器
     */
    void processNextDrive();
};

// 注册自定义类型以便在信号中使用
Q_DECLARE_METATYPE(NTFSFileScannerDuck::ScanProgress)

#endif // NTFSFILESCANNERDUCK_H
