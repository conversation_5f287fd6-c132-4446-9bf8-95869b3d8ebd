#include "FileSystemRepositoryDuck.h"
#include "DatabaseManagerDuck.h"
#include "ConcurrentBatchProcessorDuck.h"
#include "USNJournalInfoDuck.h"
#include <QDebug>
#include <QDateTime>
#include <QThread>
#include <QCoreApplication>
#include <algorithm>
#include <atomic>
#include <mutex>
#include <duckdb.h>

FileSystemRepositoryDuck::FileSystemRepositoryDuck(DatabaseManagerDuck *dbManager, QObject *parent)
    : AsyncRepositoryBaseDuck(dbManager, "FileSystemRepositoryDuck", parent),
      m_concurrentBatchingEnabled(false) {

    if (!dbManager) {
        qWarning() << "DatabaseManagerDuck is null in FileSystemRepositoryDuck constructor";
        return;
    }

    qDebug() << "FileSystemRepositoryDuck created successfully";
}

FileSystemRepositoryDuck::~FileSystemRepositoryDuck() {
    if (m_batchProcessor) {
        m_batchProcessor.reset();
    }
    qDebug() << "FileSystemRepositoryDuck destroyed";
}

void FileSystemRepositoryDuck::batchInsert(const QList<FileSystemInfoDuck> &fileInfos,
                                          std::function<void(const AsyncResultDuck<int> &)> callback) {
    if (fileInfos.isEmpty()) {
        if (callback) {
            AsyncResultDuck<int> result;
            result.success = false;
            result.error = "File info list is empty";
            result.data = 0;
            callback(result);
        }
        return;
    }

    executeAsync<int>(
        [this, fileInfos]() -> int {
            return performBatchInsert(fileInfos);
        },
        [this, callback](const AsyncResultDuck<int> &result) {
            emit batchInsertCompleted(result.success, result.data, result.error);
            if (callback) {
                callback(result);
            }
        },
        "batchInsert"
    );
}

void FileSystemRepositoryDuck::concurrentBatchInsert(const QList<FileSystemInfoDuck> &fileInfos,
                                                     std::function<void(const AsyncResultDuck<int> &)> callback) {
    if (!m_concurrentBatchingEnabled || !m_batchProcessor) {
        // 回退到普通批量插入
        batchInsert(fileInfos, callback);
        return;
    }

    // 使用并发批处理器
    // 这里应该实现具体的并发批处理逻辑
    batchInsert(fileInfos, callback);
}

int FileSystemRepositoryDuck::batchInsertFileInfosSync(const QList<FileSystemInfoDuck> &fileInfos) {
    if (fileInfos.isEmpty()) {
        return 0;
    }

    try {
        return performBatchInsert(fileInfos);
    } catch (const std::exception& e) {
        qWarning() << "Synchronous batch insert failed:" << e.what();
        return 0;
    }
}

void FileSystemRepositoryDuck::enableConcurrentBatching(bool enable, int workerThreads) {
    m_concurrentBatchingEnabled = enable;
    
    if (enable && !m_batchProcessor) {
        // 创建并发批处理器
        // m_batchProcessor = std::make_unique<ConcurrentBatchProcessorDuck>(workerThreads);
        qDebug() << "Concurrent batching enabled with" << workerThreads << "worker threads";
    } else if (!enable && m_batchProcessor) {
        m_batchProcessor.reset();
        qDebug() << "Concurrent batching disabled";
    }
}

void FileSystemRepositoryDuck::searchByFileName(const QString &fileName,
                                               const QString &diskIdentifier,
                                               std::function<void(const AsyncResultDuck<std::vector<FileSystemInfoDuck> > &)> callback) {
    QString searchFileName = fileName;
    if (searchFileName.contains('*')) {
        searchFileName.replace('*', '%');
    }
    QString condition = QString("file_name LIKE '%1'").arg(searchFileName);
    QString sql = buildSearchSQL(condition, diskIdentifier);
    
    executeAsync<std::vector<FileSystemInfoDuck>>(
        [this, sql]() -> std::vector<FileSystemInfoDuck> {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }
            
            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Query execution failed");
            }
            
            std::vector<FileSystemInfoDuck> fileInfos = parseQueryResult(result);
            duckdb_destroy_result(&result);
            releaseConnection(connection);
            
            return fileInfos;
        },
        callback,
        "searchByFileName"
    );
}

void FileSystemRepositoryDuck::searchByFilePath(const QString &filePath,
                                               const QString &diskIdentifier,
                                               std::function<void(const AsyncResultDuck<std::vector<FileSystemInfoDuck> > &)> callback) {
    QString searchFilePath = filePath;
    if (searchFilePath.contains('*')) {
        searchFilePath.replace('*', '%');
    }
    QString condition = QString("file_path LIKE '%1'").arg(searchFilePath);
    QString sql = buildSearchSQL(condition, diskIdentifier);
    
    executeAsync<std::vector<FileSystemInfoDuck>>(
        [this, sql]() -> std::vector<FileSystemInfoDuck> {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }
            
            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Query execution failed");
            }
            
            std::vector<FileSystemInfoDuck> fileInfos = parseQueryResult(result);
            duckdb_destroy_result(&result);
            releaseConnection(connection);
            
            return fileInfos;
        },
        callback,
        "searchByFilePath"
    );
}

void FileSystemRepositoryDuck::searchByFileSize(long long minSize, long long maxSize,
                                               const QString &diskIdentifier,
                                               std::function<void(const AsyncResultDuck<std::vector<FileSystemInfoDuck> > &)> callback) {
    QString condition = QString("file_size BETWEEN %1 AND %2").arg(minSize).arg(maxSize);
    QString sql = buildSearchSQL(condition, diskIdentifier);
    
    executeAsync<std::vector<FileSystemInfoDuck>>(
        [this, sql]() -> std::vector<FileSystemInfoDuck> {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }
            
            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Query execution failed");
            }
            
            std::vector<FileSystemInfoDuck> fileInfos = parseQueryResult(result);
            duckdb_destroy_result(&result);
            releaseConnection(connection);
            
            return fileInfos;
        },
        callback,
        "searchByFileSize"
    );
}

void FileSystemRepositoryDuck::searchByTimeRange(const QDateTime &startTime, const QDateTime &endTime,
                                                const QString &timeType,
                                                const QString &diskIdentifier,
                                                std::function<void(const AsyncResultDuck<std::vector<FileSystemInfoDuck> > &)> callback) {
    QString timeColumn = timeType == "created" ? "created_time" : 
                        timeType == "accessed" ? "accessed_time" : "modified_time";
    
    QString condition = QString("%1 BETWEEN '%2' AND '%3'")
                       .arg(timeColumn)
                       .arg(startTime.toString(Qt::ISODate))
                       .arg(endTime.toString(Qt::ISODate));
    
    QString sql = buildSearchSQL(condition, diskIdentifier);
    
    executeAsync<std::vector<FileSystemInfoDuck>>(
        [this, sql]() -> std::vector<FileSystemInfoDuck> {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }
            
            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Query execution failed");
            }
            
            std::vector<FileSystemInfoDuck> fileInfos = parseQueryResult(result);
            duckdb_destroy_result(&result);
            releaseConnection(connection);
            
            return fileInfos;
        },
        callback,
        "searchByTimeRange"
    );
}

int FileSystemRepositoryDuck::performBatchInsert(const QList<FileSystemInfoDuck> &fileInfos) {
    // 大幅减少日志输出 - 只在超大批次时输出
    if (fileInfos.size() >= 10000) {
        qDebug() << "performBatchInsert called with" << fileInfos.size() << "file infos";
    }

    duckdb_connection connection = getThreadSafeConnection();
    if (!connection) {
        qWarning() << "Failed to get database connection";
        throw std::runtime_error("Failed to get database connection");
    }

    try {
        // 几乎不输出详细日志
        // qDebug() << "Got database connection, querying max ID...";

        // 使用线程安全的ID生成方案
        // 使用原子操作预留ID范围
        static std::atomic<long long> globalNextId{1};
        static std::mutex idMutex;

        long long nextId;
        long long reservedCount = fileInfos.size();

        {
            std::lock_guard<std::mutex> lock(idMutex);
            // 第一次使用时，查询数据库中的最大ID
            if (globalNextId.load() == 1) {
                duckdb_result maxIdResult;
                duckdb_state state = duckdb_query(connection,
                    "SELECT COALESCE(MAX(id), 0) FROM file_system_info",
                    &maxIdResult);

                if (state == DuckDBSuccess) {
                    long long maxId = duckdb_value_int64(&maxIdResult, 0, 0);
                    globalNextId.store(maxId + 1);
                }
                duckdb_destroy_result(&maxIdResult);
            }

            // 原子地预留ID范围
            nextId = globalNextId.fetch_add(reservedCount);
        }

        // 不输出appender创建日志
        // qDebug() << "Creating DuckDB appender...";

        // 创建Appender
        duckdb_appender appender;
        duckdb_state state = duckdb_appender_create(connection, nullptr, "file_system_info", &appender);
        if (state == DuckDBError) {
            const char* error_msg = duckdb_appender_error(appender);
            QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown appender creation error";
            qWarning() << "Failed to create appender:" << error;
            releaseConnection(connection);
            throw std::runtime_error(QString("Failed to create appender: %1").arg(error).toStdString());
        }

        // 不输出详细信息
        // qDebug() << "Appender created successfully, starting to append" << fileInfos.size() << "records...";

        int insertedCount = 0;
        for (const auto &fileInfo : fileInfos) {
            // 开始新行
            duckdb_state begin_state = duckdb_appender_begin_row(appender);
            if (begin_state == DuckDBError) {
                const char* error_msg = duckdb_appender_error(appender);
                QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown begin row error";
                qWarning() << "Failed to begin row for record" << insertedCount << ":" << error;
                duckdb_appender_destroy(&appender);
                releaseConnection(connection);
                throw std::runtime_error(QString("Failed to begin row: %1").arg(error).toStdString());
            }

            // 手动提供ID值而不是使用DEFAULT
            duckdb_state append_state = duckdb_append_int64(appender, nextId + insertedCount);
            if (append_state == DuckDBError) {
                const char* error_msg = duckdb_appender_error(appender);
                QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown append ID error";
                qWarning() << "Failed to append ID for record" << insertedCount << ":" << error;
                duckdb_appender_destroy(&appender);
                releaseConnection(connection);
                throw std::runtime_error(QString("Failed to append ID: %1").arg(error).toStdString());
            }

            // 为其他列添加实际值
            if (duckdb_append_varchar(appender, fileInfo.getFileName().c_str()) == DuckDBError ||
                duckdb_append_varchar(appender, fileInfo.getFilePath().c_str()) == DuckDBError ||
                duckdb_append_int64(appender, fileInfo.getFileSize()) == DuckDBError ||
                duckdb_append_int64(appender, fileInfo.getMftEntry()) == DuckDBError ||
                duckdb_append_bool(appender, fileInfo.getIsDirectory()) == DuckDBError ||
                duckdb_append_bool(appender, fileInfo.getIsDeleted()) == DuckDBError ||
                duckdb_append_varchar(appender, fileInfo.getCreatedTime().c_str()) == DuckDBError ||
                duckdb_append_varchar(appender, fileInfo.getModifiedTime().c_str()) == DuckDBError ||
                duckdb_append_varchar(appender, fileInfo.getAccessedTime().c_str()) == DuckDBError ||
                duckdb_append_varchar(appender, fileInfo.getDiskIdentifier().c_str()) == DuckDBError ||
                duckdb_append_varchar(appender, fileInfo.getScanTime().c_str()) == DuckDBError) {

                const char* error_msg = duckdb_appender_error(appender);
                QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown append data error";
                qWarning() << "Failed to append data for record" << insertedCount << ":" << error;
                qWarning() << "Record details - fileName:" << QString::fromStdString(fileInfo.getFileName())
                          << "filePath:" << QString::fromStdString(fileInfo.getFilePath())
                          << "fileSize:" << fileInfo.getFileSize()
                          << "mftEntry:" << fileInfo.getMftEntry();
                duckdb_appender_destroy(&appender);
                releaseConnection(connection);
                throw std::runtime_error(QString("Failed to append data: %1").arg(error).toStdString());
            }

            // 结束行
            duckdb_state end_state = duckdb_appender_end_row(appender);
            if (end_state == DuckDBError) {
                const char* error_msg = duckdb_appender_error(appender);
                QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown end row error";
                qWarning() << "Failed to end row for record" << insertedCount << ":" << error;
                duckdb_appender_destroy(&appender);
                releaseConnection(connection);
                throw std::runtime_error(QString("Failed to end row: %1").arg(error).toStdString());
            }

            insertedCount++;

            // 每20000条记录输出一次进度（减少日志输出）
            if (insertedCount % 20000 == 0) {
                qDebug() << "Appended" << insertedCount << "records so far...";
            }
        }

        // 不输出详细信息
        // qDebug() << "Finished appending" << insertedCount << "records, flushing appender...";

        // 提交批量插入
        duckdb_state flush_state = duckdb_appender_flush(appender);
        if (flush_state == DuckDBError) {
            const char* error_msg = duckdb_appender_error(appender);
            QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown flush error";
            qWarning() << "Failed to flush appender:" << error;
            duckdb_appender_destroy(&appender);
            releaseConnection(connection);
            throw std::runtime_error(QString("Failed to flush appender: %1").arg(error).toStdString());
        }

        // 不输出成功日志
        // qDebug() << "Batch insertion completed, next available ID will be:" << (nextId + insertedCount);

        duckdb_appender_destroy(&appender);
        releaseConnection(connection);

        // 不输出完成日志
        // qDebug() << "performBatchInsert completed successfully, inserted" << insertedCount << "records";
        return insertedCount;
    } catch (const std::exception &e) {
        qWarning() << "performBatchInsert failed with exception:" << e.what();
        releaseConnection(connection);
        throw;
    } catch (...) {
        qWarning() << "performBatchInsert failed with unknown exception";
        releaseConnection(connection);
        throw;
    }
}

QString FileSystemRepositoryDuck::buildSearchSQL(const QString &baseCondition, const QString &diskIdentifier) const {
    QString sql = "SELECT id, file_name, file_path, file_size, mft_entry, is_directory, is_deleted, "
                  "created_time, modified_time, accessed_time, disk_identifier, scan_time "
                  "FROM file_system_info WHERE " + baseCondition;

    if (!diskIdentifier.isEmpty()) {
        sql += QString(" AND disk_identifier = '%1'").arg(diskIdentifier);
    }

    sql += " ORDER BY file_path, file_name";
    return sql;
}

std::vector<FileSystemInfoDuck> FileSystemRepositoryDuck::parseQueryResult(duckdb_result &result) const {
    std::vector<FileSystemInfoDuck> fileInfos;

    idx_t column_count = duckdb_column_count(&result);
    idx_t row_count = duckdb_row_count(&result);

    if (column_count == 0 || row_count == 0) {
        return fileInfos;
    }

    fileInfos.reserve(row_count);

    for (idx_t row = 0; row < row_count; row++) {
        FileSystemInfoDuck info;

        // 解析每一列的数据
        info.setId(duckdb_value_int32(&result, 0, row));

        char *fileName = duckdb_value_varchar(&result, 1, row);
        if (fileName) {
            info.setFileName(fileName);
            duckdb_free(fileName);
        }

        char *filePath = duckdb_value_varchar(&result, 2, row);
        if (filePath) {
            info.setFilePath(filePath);
            duckdb_free(filePath);
        }

        info.setFileSize(duckdb_value_int64(&result, 3, row));
        info.setMftEntry(duckdb_value_int64(&result, 4, row));
        info.setIsDirectory(duckdb_value_boolean(&result, 5, row));
        info.setIsDeleted(duckdb_value_boolean(&result, 6, row));

        char *createdTime = duckdb_value_varchar(&result, 7, row);
        if (createdTime) {
            info.setCreatedTime(createdTime);
            duckdb_free(createdTime);
        }

        char *modifiedTime = duckdb_value_varchar(&result, 8, row);
        if (modifiedTime) {
            info.setModifiedTime(modifiedTime);
            duckdb_free(modifiedTime);
        }

        char *accessedTime = duckdb_value_varchar(&result, 9, row);
        if (accessedTime) {
            info.setAccessedTime(accessedTime);
            duckdb_free(accessedTime);
        }

        char *diskIdentifier = duckdb_value_varchar(&result, 10, row);
        if (diskIdentifier) {
            info.setDiskIdentifier(diskIdentifier);
            duckdb_free(diskIdentifier);
        }

        char *scanTime = duckdb_value_varchar(&result, 11, row);
        if (scanTime) {
            info.setScanTime(scanTime);
            duckdb_free(scanTime);
        }

        fileInfos.push_back(info);
    }

    return fileInfos;
}

void FileSystemRepositoryDuck::getFileStatistics(const QString &diskIdentifier,
                                                std::function<void(const AsyncResultDuck<std::tuple<int, long long, int, int> > &)> callback) {
    QString sql = "SELECT COUNT(*) as total_files, SUM(file_size) as total_size, "
                  "SUM(CASE WHEN is_directory THEN 1 ELSE 0 END) as directory_count, "
                  "SUM(CASE WHEN is_deleted THEN 1 ELSE 0 END) as deleted_count "
                  "FROM file_system_info";

    if (!diskIdentifier.isEmpty()) {
        sql += QString(" WHERE disk_identifier = '%1'").arg(diskIdentifier);
    }

    executeAsync<std::tuple<int, long long, int, int>>(
        [this, sql]() -> std::tuple<int, long long, int, int> {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Query execution failed");
            }

            std::tuple<int, long long, int, int> stats;
            if (duckdb_row_count(&result) > 0) {
                stats = std::make_tuple(
                    duckdb_value_int32(&result, 0, 0),  // total_files
                    duckdb_value_int64(&result, 1, 0),  // total_size
                    duckdb_value_int32(&result, 2, 0),  // directory_count
                    duckdb_value_int32(&result, 3, 0)   // deleted_count
                );
            }

            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return stats;
        },
        callback,
        "getFileStatistics"
    );
}

void FileSystemRepositoryDuck::getAllDiskIdentifiers(std::function<void(const AsyncResultDuck<QStringList> &)> callback) {
    QString sql = "SELECT DISTINCT disk_identifier FROM file_system_info ORDER BY disk_identifier";

    executeAsync<QStringList>(
        [this, sql]() -> QStringList {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Query execution failed");
            }

            QStringList diskIds;
            for (idx_t row = 0; row < duckdb_row_count(&result); row++) {
                char *value = duckdb_value_varchar(&result, 0, row);
                if (value) {
                    diskIds.append(QString::fromUtf8(value));
                    duckdb_free(value);
                }
            }

            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return diskIds;
        },
        callback,
        "getAllDiskIdentifiers"
    );
}

void FileSystemRepositoryDuck::checkTableExists(std::function<void(const AsyncResultDuck<bool> &)> callback) {
    QString sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'file_system_info'";

    executeAsync<bool>(
        [this, sql]() -> bool {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Query execution failed");
            }

            bool exists = false;
            if (duckdb_row_count(&result) > 0) {
                exists = duckdb_value_int32(&result, 0, 0) > 0;
            }

            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return exists;
        },
        callback,
        "checkTableExists"
    );
}

void FileSystemRepositoryDuck::getFileSizeDistribution(const QString &diskIdentifier,
                                                      std::function<void(const AsyncResultDuck<std::vector<std::tuple<QString, int, long long> > > &)> callback) {
    QString sql = "SELECT "
                  "CASE "
                  "  WHEN file_size < 1024 THEN 'Small (<1KB)' "
                  "  WHEN file_size < 1048576 THEN 'Medium (1KB-1MB)' "
                  "  WHEN file_size < 1073741824 THEN 'Large (1MB-1GB)' "
                  "  ELSE 'Very Large (>1GB)' "
                  "END as size_category, "
                  "COUNT(*) as file_count, "
                  "SUM(file_size) as total_size "
                  "FROM file_system_info";

    if (!diskIdentifier.isEmpty()) {
        sql += QString(" WHERE disk_identifier = '%1'").arg(diskIdentifier);
    }

    sql += " GROUP BY size_category ORDER BY total_size DESC";

    executeAsync<std::vector<std::tuple<QString, int, long long>>>(
        [this, sql]() -> std::vector<std::tuple<QString, int, long long>> {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Query execution failed");
            }

            std::vector<std::tuple<QString, int, long long>> distribution;
            for (idx_t row = 0; row < duckdb_row_count(&result); row++) {
                char *categoryStr = duckdb_value_varchar(&result, 0, row);
                QString category = categoryStr ? QString::fromUtf8(categoryStr) : QString();
                if (categoryStr) {
                    duckdb_free(categoryStr);
                }

                int count = duckdb_value_int32(&result, 1, row);
                long long totalSize = duckdb_value_int64(&result, 2, row);

                distribution.emplace_back(category, count, totalSize);
            }

            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return distribution;
        },
        callback,
        "getFileSizeDistribution"
    );
}

void FileSystemRepositoryDuck::getFileTypeDistribution(const QString &diskIdentifier,
                                                      std::function<void(const AsyncResultDuck<std::vector<std::tuple<QString, int, long long> > > &)> callback) {
    QString sql = "SELECT "
                  "CASE "
                  "  WHEN file_name LIKE '%.txt' THEN 'Text Files' "
                  "  WHEN file_name LIKE '%.doc%' THEN 'Documents' "
                  "  WHEN file_name LIKE '%.pdf' THEN 'PDF Files' "
                  "  WHEN file_name LIKE '%.jpg' OR file_name LIKE '%.png' OR file_name LIKE '%.gif' THEN 'Images' "
                  "  WHEN file_name LIKE '%.mp3' OR file_name LIKE '%.wav' THEN 'Audio' "
                  "  WHEN file_name LIKE '%.mp4' OR file_name LIKE '%.avi' THEN 'Video' "
                  "  WHEN file_name LIKE '%.exe' OR file_name LIKE '%.dll' THEN 'Executables' "
                  "  ELSE 'Other' "
                  "END as file_type, "
                  "COUNT(*) as file_count, "
                  "SUM(file_size) as total_size "
                  "FROM file_system_info "
                  "WHERE is_directory = false";

    if (!diskIdentifier.isEmpty()) {
        sql += QString(" AND disk_identifier = '%1'").arg(diskIdentifier);
    }

    sql += " GROUP BY file_type ORDER BY file_count DESC";

    executeAsync<std::vector<std::tuple<QString, int, long long>>>(
        [this, sql]() -> std::vector<std::tuple<QString, int, long long>> {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Query execution failed");
            }

            std::vector<std::tuple<QString, int, long long>> distribution;
            for (idx_t row = 0; row < duckdb_row_count(&result); row++) {
                char *fileTypeStr = duckdb_value_varchar(&result, 0, row);
                QString fileType = fileTypeStr ? QString::fromUtf8(fileTypeStr) : QString();
                if (fileTypeStr) {
                    duckdb_free(fileTypeStr);
                }

                int count = duckdb_value_int32(&result, 1, row);
                long long totalSize = duckdb_value_int64(&result, 2, row);

                distribution.emplace_back(fileType, count, totalSize);
            }

            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return distribution;
        },
        callback,
        "getFileTypeDistribution"
    );
}

void FileSystemRepositoryDuck::executeAnalyticsQuery(const QString &sql,
                                                    std::function<void(const AsyncResultDuck<std::vector<std::vector<QString> > > &)> callback) {
    executeAsync<std::vector<std::vector<QString>>>(
        [this, sql]() -> std::vector<std::vector<QString>> {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Query execution failed");
            }

            std::vector<std::vector<QString>> queryResult;
            idx_t rowCount = duckdb_row_count(&result);
            idx_t columnCount = duckdb_column_count(&result);

            for (idx_t row = 0; row < rowCount; row++) {
                std::vector<QString> rowData;
                for (idx_t col = 0; col < columnCount; col++) {
                    char *value = duckdb_value_varchar(&result, col, row);
                    rowData.push_back(QString::fromUtf8(value ? value : ""));
                    if (value) {
                        duckdb_free(value);
                    }
                }
                queryResult.push_back(rowData);
            }

            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return queryResult;
        },
        callback,
        "executeAnalyticsQuery"
    );
}

void FileSystemRepositoryDuck::createIndexes(std::function<void(const AsyncResultDuck<bool> &)> callback) {
    executeAsync<bool>(
        [this]() -> bool {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            // 创建常用字段的索引
            std::vector<QString> indexQueries = {
                "CREATE INDEX IF NOT EXISTS idx_file_name ON file_system_info(file_name)",
                "CREATE INDEX IF NOT EXISTS idx_file_path ON file_system_info(file_path)",
                "CREATE INDEX IF NOT EXISTS idx_file_size ON file_system_info(file_size)",
                "CREATE INDEX IF NOT EXISTS idx_disk_identifier ON file_system_info(disk_identifier)",
                "CREATE INDEX IF NOT EXISTS idx_is_directory ON file_system_info(is_directory)",
                "CREATE INDEX IF NOT EXISTS idx_modified_time ON file_system_info(modified_time)"
            };

            for (const QString &query : indexQueries) {
                duckdb_result result;
                if (duckdb_query(connection, query.toUtf8().constData(), &result) == DuckDBError) {
                    duckdb_destroy_result(&result);
                    releaseConnection(connection);
                    throw std::runtime_error("Failed to create index: " + query.toStdString());
                }
                duckdb_destroy_result(&result);
            }

            releaseConnection(connection);
            return true;
        },
        callback,
        "createIndexes"
    );
}

void FileSystemRepositoryDuck::optimizeDatabase(std::function<void(const AsyncResultDuck<bool> &)> callback) {
    executeAsync<bool>(
        [this]() -> bool {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            // 执行ANALYZE命令优化查询计划
            duckdb_result result;
            if (duckdb_query(connection, "ANALYZE file_system_info", &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Failed to analyze table");
            }

            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return true;
        },
        callback,
        "optimizeDatabase"
    );
}

void FileSystemRepositoryDuck::clearDiskData(const QString &diskIdentifier,
                                            std::function<void(const AsyncResultDuck<bool> &)> callback) {
    QString sql = QString("DELETE FROM file_system_info WHERE disk_identifier = '%1'").arg(diskIdentifier);

    executeAsync<bool>(
        [this, sql]() -> bool {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Failed to clear disk data");
            }

            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return true;
        },
        callback,
        "clearDiskData"
    );
}

void FileSystemRepositoryDuck::removeDuplicates(std::function<void(const AsyncResultDuck<int> &)> callback) {
    executeAsync<int>(
        [this]() -> int {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            // 删除重复记录，保留ID最小的记录
            QString sql = "DELETE FROM file_system_info WHERE id NOT IN ("
                         "SELECT MIN(id) FROM file_system_info "
                         "GROUP BY file_path, disk_identifier, mft_entry)";

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Failed to remove duplicates");
            }

            // 获取删除的行数（DuckDB可能不支持直接获取affected rows）
            int deletedCount = 0; // 简化实现

            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return deletedCount;
        },
        callback,
        "removeDuplicates"
    );
}

// USN相关方法实现

void FileSystemRepositoryDuck::batchInsertUSN(const std::vector<USNJournalInfoDuck> &usnInfos,
                                              std::function<void(const AsyncResultDuck<int> &)> callback) {
    if (usnInfos.empty()) {
        if (callback) {
            AsyncResultDuck<int> result;
            result.success = false;
            result.error = "USN info list is empty";
            result.data = 0;
            callback(result);
        }
        return;
    }

    executeAsync<int>(
        [this, usnInfos]() -> int {
            return performUSNBatchInsert(usnInfos);
        },
        [this, callback](const AsyncResultDuck<int> &result) {
            emit batchInsertCompleted(result.success, result.data, result.error);
            if (callback) {
                callback(result);
            }
        },
        "batchInsertUSN"
    );
}

void FileSystemRepositoryDuck::concurrentBatchInsertUSN(const std::vector<USNJournalInfoDuck> &usnInfos,
                                                        std::function<void(const AsyncResultDuck<int> &)> callback) {
    if (!m_concurrentBatchingEnabled || !m_batchProcessor) {
        // 回退到普通批量插入
        batchInsertUSN(usnInfos, callback);
        return;
    }

    // 使用并发批处理器
    // 这里应该实现具体的并发批处理逻辑
    batchInsertUSN(usnInfos, callback);
}

int FileSystemRepositoryDuck::performUSNBatchInsert(const std::vector<USNJournalInfoDuck> &usnInfos) {
    if (usnInfos.size() >= 1000) {
        qDebug() << "performUSNBatchInsert called with" << usnInfos.size() << "USN infos";
    }

    duckdb_connection connection = getThreadSafeConnection();
    if (!connection) {
        qWarning() << "Failed to get database connection";
        throw std::runtime_error("Failed to get database connection");
    }

    try {
        // 获取当前最大ID
        duckdb_result maxIdResult;
        duckdb_state state = duckdb_query(connection,
            "SELECT COALESCE(MAX(id), 0) FROM usn_journal_info",
            &maxIdResult);

        long long nextId = 1;
        if (state == DuckDBSuccess) {
            nextId = duckdb_value_int64(&maxIdResult, 0, 0) + 1;
        } else {
            qWarning() << "Failed to query max USN ID, using default nextId = 1";
        }
        duckdb_destroy_result(&maxIdResult);

        // 创建Appender
        duckdb_appender appender;
        state = duckdb_appender_create(connection, nullptr, "usn_journal_info", &appender);
        if (state == DuckDBError) {
            const char* error_msg = duckdb_appender_error(appender);
            QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown appender creation error";
            qWarning() << "Failed to create USN appender:" << error;
            releaseConnection(connection);
            throw std::runtime_error(QString("Failed to create USN appender: %1").arg(error).toStdString());
        }

        int insertedCount = 0;
        for (const auto &usnInfo : usnInfos) {
            // 开始新行
            duckdb_state begin_state = duckdb_appender_begin_row(appender);
            if (begin_state == DuckDBError) {
                const char* error_msg = duckdb_appender_error(appender);
                QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown begin row error";
                qWarning() << "Failed to begin USN row for record" << insertedCount << ":" << error;
                duckdb_appender_destroy(&appender);
                releaseConnection(connection);
                throw std::runtime_error(QString("Failed to begin USN row: %1").arg(error).toStdString());
            }

            // 添加所有字段
            if (duckdb_append_int64(appender, nextId + insertedCount) == DuckDBError ||
                duckdb_append_varchar(appender, usnInfo.getFileName().c_str()) == DuckDBError ||
                duckdb_append_varchar(appender, usnInfo.getFilePath().c_str()) == DuckDBError ||
                duckdb_append_int64(appender, usnInfo.getFileReferenceNumber()) == DuckDBError ||
                duckdb_append_int64(appender, usnInfo.getParentFileReferenceNumber()) == DuckDBError ||
                duckdb_append_int64(appender, usnInfo.getUsn()) == DuckDBError ||
                duckdb_append_int64(appender, usnInfo.getTimeStamp()) == DuckDBError ||
                duckdb_append_int32(appender, usnInfo.getReason()) == DuckDBError ||
                duckdb_append_int32(appender, usnInfo.getSourceInfo()) == DuckDBError ||
                duckdb_append_int32(appender, usnInfo.getFileAttributes()) == DuckDBError ||
                duckdb_append_varchar(appender, usnInfo.getDiskIdentifier().c_str()) == DuckDBError ||
                duckdb_append_varchar(appender, usnInfo.getScanTime().c_str()) == DuckDBError) {

                const char* error_msg = duckdb_appender_error(appender);
                QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown append USN data error";
                qWarning() << "Failed to append USN data for record" << insertedCount << ":" << error;
                duckdb_appender_destroy(&appender);
                releaseConnection(connection);
                throw std::runtime_error(QString("Failed to append USN data: %1").arg(error).toStdString());
            }

            // 结束行
            duckdb_state end_state = duckdb_appender_end_row(appender);
            if (end_state == DuckDBError) {
                const char* error_msg = duckdb_appender_error(appender);
                QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown end row error";
                qWarning() << "Failed to end USN row for record" << insertedCount << ":" << error;
                duckdb_appender_destroy(&appender);
                releaseConnection(connection);
                throw std::runtime_error(QString("Failed to end USN row: %1").arg(error).toStdString());
            }

            insertedCount++;

            // 每1000条记录输出一次进度
            if (insertedCount % 1000 == 0) {
                qDebug() << "Appended" << insertedCount << "USN records so far...";
            }
        }

        // 提交批量插入
        duckdb_state flush_state = duckdb_appender_flush(appender);
        if (flush_state == DuckDBError) {
            const char* error_msg = duckdb_appender_error(appender);
            QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown flush error";
            qWarning() << "Failed to flush USN appender:" << error;
            duckdb_appender_destroy(&appender);
            releaseConnection(connection);
            throw std::runtime_error(QString("Failed to flush USN appender: %1").arg(error).toStdString());
        }

        duckdb_appender_destroy(&appender);
        releaseConnection(connection);

        if (insertedCount >= 100) {
            qDebug() << "USN batch insertion completed, inserted" << insertedCount << "records";
        }
        return insertedCount;
    } catch (const std::exception &e) {
        qWarning() << "performUSNBatchInsert failed with exception:" << e.what();
        releaseConnection(connection);
        throw;
    } catch (...) {
        qWarning() << "performUSNBatchInsert failed with unknown exception";
        releaseConnection(connection);
        throw;
    }
}

void FileSystemRepositoryDuck::searchUSNByRange(long long startUsn, long long endUsn,
                                                const QString &diskIdentifier,
                                                std::function<void(const AsyncResultDuck<std::vector<USNJournalInfoDuck> > &)> callback) {
    QString condition = QString("usn BETWEEN %1 AND %2").arg(startUsn).arg(endUsn);
    QString sql = "SELECT id, file_name, file_path, file_reference_number, parent_file_reference_number, "
                  "usn, time_stamp, reason, source_info, file_attributes, disk_identifier, scan_time "
                  "FROM usn_journal_info WHERE " + condition;

    if (!diskIdentifier.isEmpty()) {
        sql += QString(" AND disk_identifier = '%1'").arg(diskIdentifier);
    }

    sql += " ORDER BY usn";

    executeAsync<std::vector<USNJournalInfoDuck>>(
        [this, sql]() -> std::vector<USNJournalInfoDuck> {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("USN query execution failed");
            }

            std::vector<USNJournalInfoDuck> usnInfos = parseUSNQueryResult(result);
            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return usnInfos;
        },
        callback,
        "searchUSNByRange"
    );
}

void FileSystemRepositoryDuck::searchUSNByReason(unsigned int reason,
                                                 const QString &diskIdentifier,
                                                 std::function<void(const AsyncResultDuck<std::vector<USNJournalInfoDuck> > &)> callback) {
    QString condition = QString("reason & %1 = %1").arg(reason);
    QString sql = "SELECT id, file_name, file_path, file_reference_number, parent_file_reference_number, "
                  "usn, time_stamp, reason, source_info, file_attributes, disk_identifier, scan_time "
                  "FROM usn_journal_info WHERE " + condition;

    if (!diskIdentifier.isEmpty()) {
        sql += QString(" AND disk_identifier = '%1'").arg(diskIdentifier);
    }

    sql += " ORDER BY time_stamp DESC";

    executeAsync<std::vector<USNJournalInfoDuck>>(
        [this, sql]() -> std::vector<USNJournalInfoDuck> {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("USN query execution failed");
            }

            std::vector<USNJournalInfoDuck> usnInfos = parseUSNQueryResult(result);
            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return usnInfos;
        },
        callback,
        "searchUSNByReason"
    );
}

void FileSystemRepositoryDuck::getUSNStatistics(const QString &diskIdentifier,
                                               std::function<void(const AsyncResultDuck<std::tuple<int, long long, long long> > &)> callback) {
    QString sql = "SELECT COUNT(*) as total_records, MIN(usn) as min_usn, MAX(usn) as max_usn "
                  "FROM usn_journal_info";

    if (!diskIdentifier.isEmpty()) {
        sql += QString(" WHERE disk_identifier = '%1'").arg(diskIdentifier);
    }

    executeAsync<std::tuple<int, long long, long long>>(
        [this, sql]() -> std::tuple<int, long long, long long> {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("USN statistics query execution failed");
            }

            std::tuple<int, long long, long long> stats;
            if (duckdb_row_count(&result) > 0) {
                stats = std::make_tuple(
                    duckdb_value_int32(&result, 0, 0),  // total_records
                    duckdb_value_int64(&result, 1, 0),  // min_usn
                    duckdb_value_int64(&result, 2, 0)   // max_usn
                );
            }

            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return stats;
        },
        callback,
        "getUSNStatistics"
    );
}

void FileSystemRepositoryDuck::clearUSNData(const QString &diskIdentifier,
                                           std::function<void(const AsyncResultDuck<bool> &)> callback) {
    QString sql = QString("DELETE FROM usn_journal_info WHERE disk_identifier = '%1'").arg(diskIdentifier);

    executeAsync<bool>(
        [this, sql]() -> bool {
            duckdb_connection connection = getThreadSafeConnection();
            if (!connection) {
                throw std::runtime_error("Failed to get database connection");
            }

            duckdb_result result;
            if (duckdb_query(connection, sql.toUtf8().constData(), &result) == DuckDBError) {
                releaseConnection(connection);
                throw std::runtime_error("Failed to clear USN data");
            }

            duckdb_destroy_result(&result);
            releaseConnection(connection);

            return true;
        },
        callback,
        "clearUSNData"
    );
}

std::vector<USNJournalInfoDuck> FileSystemRepositoryDuck::parseUSNQueryResult(duckdb_result &result) const {
    std::vector<USNJournalInfoDuck> usnInfos;

    idx_t column_count = duckdb_column_count(&result);
    idx_t row_count = duckdb_row_count(&result);

    if (column_count == 0 || row_count == 0) {
        return usnInfos;
    }

    usnInfos.reserve(row_count);

    for (idx_t row = 0; row < row_count; row++) {
        USNJournalInfoDuck info;

        // 解析每一列的数据
        info.setId(duckdb_value_int32(&result, 0, row));

        char *fileName = duckdb_value_varchar(&result, 1, row);
        if (fileName) {
            info.setFileName(fileName);
            duckdb_free(fileName);
        }

        char *filePath = duckdb_value_varchar(&result, 2, row);
        if (filePath) {
            info.setFilePath(filePath);
            duckdb_free(filePath);
        }

        info.setFileReferenceNumber(duckdb_value_int64(&result, 3, row));
        info.setParentFileReferenceNumber(duckdb_value_int64(&result, 4, row));
        info.setUsn(duckdb_value_int64(&result, 5, row));
        info.setTimeStamp(duckdb_value_int64(&result, 6, row));
        info.setReason(duckdb_value_int32(&result, 7, row));
        info.setSourceInfo(duckdb_value_int32(&result, 8, row));
        info.setFileAttributes(duckdb_value_int32(&result, 9, row));

        char *diskIdentifier = duckdb_value_varchar(&result, 10, row);
        if (diskIdentifier) {
            info.setDiskIdentifier(diskIdentifier);
            duckdb_free(diskIdentifier);
        }

        char *scanTime = duckdb_value_varchar(&result, 11, row);
        if (scanTime) {
            info.setScanTime(scanTime);
            duckdb_free(scanTime);
        }

        usnInfos.push_back(info);
    }

    return usnInfos;
}
