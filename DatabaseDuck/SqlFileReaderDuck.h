#ifndef SQLFILEREADERDUCK_H
#define SQLFILEREADERDUCK_H

#include <QString>
#include <QStringList>
#include <QObject>

/**
 * @brief SQL文件读取器类（DuckDB版本）
 *
 * 提供SQL文件读取和解析功能，支持：
 * - 读取资源文件中的SQL文件
 * - 读取本地文件系统中的SQL文件
 * - 解析SQL语句（去除注释、分割语句等）
 * - 支持多种SQL文件格式
 */
class SqlFileReaderDuck : public QObject {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit SqlFileReaderDuck(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~SqlFileReaderDuck() override;

    /**
     * @brief 读取SQL文件内容
     * @param filePath 文件路径（支持资源文件路径，如 ":/sql/table.sql"）
     * @return SQL文件内容，失败时返回空字符串
     */
    QString readSqlFile(const QString &filePath);

    /**
     * @brief 读取SQL文件并解析为语句列表
     * @param filePath 文件路径
     * @return SQL语句列表，每个元素为一条完整的SQL语句
     */
    QStringList readSqlStatements(const QString &filePath);

    /**
     * @brief 解析SQL内容为语句列表
     * @param sqlContent SQL文件内容
     * @return SQL语句列表
     */
    QStringList parseSqlStatements(const QString &sqlContent);

    /**
     * @brief 验证SQL文件是否存在且可读
     * @param filePath 文件路径
     * @return 存在且可读返回true
     */
    bool validateSqlFile(const QString &filePath);

    /**
     * @brief 获取SQL文件的编码格式
     * @param filePath 文件路径
     * @return 编码格式字符串
     */
    QString detectFileEncoding(const QString &filePath);

    /**
     * @brief 清理SQL语句（去除注释、多余空白等）
     * @param sql 原始SQL语句
     * @return 清理后的SQL语句
     */
    QString cleanSqlStatement(const QString &sql);

    /**
     * @brief 检查SQL语句是否为DDL语句
     * @param sql SQL语句
     * @return 是DDL语句返回true
     */
    bool isDDLStatement(const QString &sql);

    /**
     * @brief 检查SQL语句是否为DML语句
     * @param sql SQL语句
     * @return 是DML语句返回true
     */
    bool isDMLStatement(const QString &sql);

    /**
     * @brief 获取最后一次操作的错误信息
     * @return 错误信息字符串
     */
    QString getLastError() const;

signals:
    /**
     * @brief SQL文件读取完成信号
     * @param filePath 文件路径
     * @param success 是否成功
     * @param content 文件内容（成功时）
     * @param error 错误信息（失败时）
     */
    void sqlFileRead(const QString &filePath, bool success, const QString &content, const QString &error);

    /**
     * @brief SQL语句解析完成信号
     * @param statements 解析出的SQL语句列表
     * @param count 语句数量
     */
    void sqlStatementsParsed(const QStringList &statements, int count);

private:
    QString m_lastError;                    ///< 最后一次错误信息

    /**
     * @brief 读取资源文件内容
     * @param resourcePath 资源文件路径
     * @return 文件内容
     */
    QString readResourceFile(const QString &resourcePath);

    /**
     * @brief 读取本地文件内容
     * @param localPath 本地文件路径
     * @return 文件内容
     */
    QString readLocalFile(const QString &localPath);

    /**
     * @brief 去除SQL注释
     * @param sql SQL内容
     * @return 去除注释后的SQL内容
     */
    QString removeComments(const QString &sql);

    /**
     * @brief 分割SQL语句
     * @param sql SQL内容
     * @return SQL语句列表
     */
    QStringList splitStatements(const QString &sql);

    /**
     * @brief 检查是否为资源文件路径
     * @param filePath 文件路径
     * @return 是资源文件路径返回true
     */
    bool isResourcePath(const QString &filePath);

    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setLastError(const QString &error);
};

#endif // SQLFILEREADERDUCK_H
