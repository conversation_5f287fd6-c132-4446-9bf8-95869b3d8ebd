<RCC>
    <qresource prefix="/sql/real_test.duckdb">
        <file>sql/file_system_info_duck.sql</file>
    </qresource>
    <qresource prefix="/sql/test_filesystem_repository.duckdb">
        <file>sql/file_system_info_duck.sql</file>
    </qresource>
    <qresource prefix="/sql/test_mft_segment.duckdb">
        <file>sql/file_system_info_duck.sql</file>
    </qresource>
    <qresource prefix="/sql/ntfs_integration_test.duckdb">
        <file>sql/file_system_info_duck.sql</file>
    </qresource>
    <qresource prefix="/sql/test_scanner_duck.duckdb">
        <file>sql/file_system_info_duck.sql</file>
    </qresource>
    <qresource prefix="/sql/test_fileduck.duckdb">
        <file>sql/file_system_info_duck.sql</file>
        <file>sql/usn_journal_info_duck.sql</file>
    </qresource>
    <qresource prefix="/sql/usn_test.duckdb">
        <file>sql/usn_journal_info_duck.sql</file>
    </qresource>
    <qresource prefix="/sql/usn_scanner_test.duckdb">
        <file>sql/usn_journal_info_duck.sql</file>
    </qresource>
    <qresource prefix="/sql/test_ntfs_high_performance.duckdb">
        <file>sql/usn_journal_info_duck.sql</file>
    </qresource>
</RCC>
