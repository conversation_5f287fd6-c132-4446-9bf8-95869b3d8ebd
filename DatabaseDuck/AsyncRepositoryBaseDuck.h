#ifndef ASYNCREPOSITORYBASEDUCK_H
#define ASYNCREPOSITORYBASEDUCK_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QMutexLocker>
#include <QTimer>
#include <functional>
#include <memory>
#include <atomic>
#include <QDateTime>

#include "duckdb.h"

// 前向声明
class DatabaseManagerDuck;

/**
 * @brief 异步操作结果模板类（DuckDB版本）
 */
template<typename T>
struct AsyncResultDuck {
    bool success;           ///< 操作是否成功
    T data;                ///< 操作结果数据
    QString error;         ///< 错误信息（失败时）
    QString operationName; ///< 操作名称
    qint64 executionTime;  ///< 执行时间（毫秒）

    AsyncResultDuck() : success(false), executionTime(0) {}
    
    AsyncResultDuck(bool s, const T& d, const QString& e = QString(), 
                   const QString& op = QString(), qint64 time = 0)
        : success(s), data(d), error(e), operationName(op), executionTime(time) {}
};

/**
 * @brief 异步仓库基类（DuckDB版本）
 *
 * 提供异步数据库操作的基础功能：
 * - 线程安全的异步操作执行
 * - 连接池管理
 * - 错误处理和重试机制
 * - 性能监控和统计
 */
class AsyncRepositoryBaseDuck : public QObject {
    Q_OBJECT

public:
    /**
     * @brief 构造函数
     * @param dbManager 数据库管理器指针
     * @param repositoryName 仓库名称
     * @param parent 父对象
     */
    explicit AsyncRepositoryBaseDuck(DatabaseManagerDuck *dbManager, 
                                    const QString &repositoryName = "AsyncRepositoryDuck",
                                    QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~AsyncRepositoryBaseDuck() override;

    /**
     * @brief 启动异步仓库服务
     */
    virtual void start();

    /**
     * @brief 停止异步仓库服务
     */
    virtual void stop();

    /**
     * @brief 检查仓库是否正在运行
     * @return 正在运行返回true
     */
    bool isRunning() const;

    /**
     * @brief 获取仓库名称
     * @return 仓库名称
     */
    QString getRepositoryName() const;

    /**
     * @brief 获取操作统计信息
     * @return 统计信息字符串
     */
    QString getStatistics() const;

    /**
     * @brief 重置统计信息
     */
    void resetStatistics();

signals:
    /**
     * @brief 仓库启动完成信号
     * @param repositoryName 仓库名称
     */
    void repositoryStarted(const QString &repositoryName);

    /**
     * @brief 仓库停止完成信号
     * @param repositoryName 仓库名称
     */
    void repositoryStopped(const QString &repositoryName);

    /**
     * @brief 操作完成信号
     * @param operationName 操作名称
     * @param success 是否成功
     * @param executionTime 执行时间
     */
    void operationCompleted(const QString &operationName, bool success, qint64 executionTime);

protected:
    /**
     * @brief 执行异步操作
     * @param operation 操作函数
     * @param callback 回调函数
     * @param operationName 操作名称
     * @param retryCount 重试次数
     */
    template<typename T>
    void executeAsync(std::function<T()> operation,
                      std::function<void(const AsyncResultDuck<T> &)> callback,
                      const QString &operationName,
                      int retryCount = 0);

    /**
     * @brief 获取线程安全的数据库连接
     * @return 数据库连接句柄
     */
    duckdb_connection getThreadSafeConnection();

    /**
     * @brief 释放数据库连接
     * @param connection 数据库连接句柄
     */
    void releaseConnection(duckdb_connection connection);

    /**
     * @brief 获取数据库管理器
     * @return 数据库管理器指针
     */
    DatabaseManagerDuck* getDatabaseManager() const;

    /**
     * @brief 记录操作统计
     * @param operationName 操作名称
     * @param success 是否成功
     * @param executionTime 执行时间
     */
    void recordStatistics(const QString &operationName, bool success, qint64 executionTime);

    /**
     * @brief 处理数据库错误
     * @param error 错误信息
     * @param operationName 操作名称
     * @return 是否应该重试
     */
    bool handleDatabaseError(const QString &error, const QString &operationName);

private slots:
    /**
     * @brief 定期清理资源
     */
    void performPeriodicCleanup();

private:
    DatabaseManagerDuck *m_dbManager;      ///< 数据库管理器
    QString m_repositoryName;              ///< 仓库名称
    QThread *m_workerThread;               ///< 工作线程
    std::atomic<bool> m_running;           ///< 是否正在运行
    mutable QMutex m_mutex;                ///< 线程安全保护
    QTimer *m_cleanupTimer;                ///< 清理定时器

    // 统计信息
    std::atomic<qint64> m_totalOperations;     ///< 总操作数
    std::atomic<qint64> m_successfulOperations; ///< 成功操作数
    std::atomic<qint64> m_failedOperations;    ///< 失败操作数
    std::atomic<qint64> m_totalExecutionTime;  ///< 总执行时间
    std::atomic<qint64> m_maxExecutionTime;    ///< 最大执行时间
    std::atomic<qint64> m_minExecutionTime;    ///< 最小执行时间

    /**
     * @brief 初始化工作线程
     */
    void initializeWorkerThread();

    /**
     * @brief 清理工作线程
     */
    void cleanupWorkerThread();

    /**
     * @brief 更新执行时间统计
     * @param executionTime 执行时间
     */
    void updateExecutionTimeStats(qint64 executionTime);
};

// 模板方法实现
template<typename T>
void AsyncRepositoryBaseDuck::executeAsync(std::function<T()> operation,
                                          std::function<void(const AsyncResultDuck<T> &)> callback,
                                          const QString &operationName,
                                          int retryCount) {
    if (!m_running) {
        if (callback) {
            AsyncResultDuck<T> result(false, T(), "Repository not running", operationName, 0);
            callback(result);
        }
        return;
    }

    // 在工作线程中执行操作
    QMetaObject::invokeMethod(this, [this, operation, callback, operationName, retryCount]() {
        qint64 startTime = QDateTime::currentMSecsSinceEpoch();
        AsyncResultDuck<T> result;
        result.operationName = operationName;

        try {
            T data = operation();
            qint64 endTime = QDateTime::currentMSecsSinceEpoch();
            result.executionTime = endTime - startTime;
            result.success = true;
            result.data = data;

            recordStatistics(operationName, true, result.executionTime);
            emit operationCompleted(operationName, true, result.executionTime);

        } catch (const std::exception &ex) {
            qint64 endTime = QDateTime::currentMSecsSinceEpoch();
            result.executionTime = endTime - startTime;
            result.success = false;
            result.error = QString("Operation failed: %1").arg(ex.what());

            // 检查是否应该重试
            if (retryCount > 0 && handleDatabaseError(result.error, operationName)) {
                qDebug() << "Retrying operation" << operationName << "(" << retryCount << "retries left)";
                executeAsync<T>(operation, callback, operationName, retryCount - 1);
                return;
            }

            recordStatistics(operationName, false, result.executionTime);
            emit operationCompleted(operationName, false, result.executionTime);

        } catch (...) {
            qint64 endTime = QDateTime::currentMSecsSinceEpoch();
            result.executionTime = endTime - startTime;
            result.success = false;
            result.error = "Unknown error occurred";

            recordStatistics(operationName, false, result.executionTime);
            emit operationCompleted(operationName, false, result.executionTime);
        }

        if (callback) {
            callback(result);
        }
    }, Qt::QueuedConnection);
}

#endif // ASYNCREPOSITORYBASEDUCK_H
