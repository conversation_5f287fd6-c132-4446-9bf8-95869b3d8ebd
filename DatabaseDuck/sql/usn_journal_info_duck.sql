-- 创建序列用于自增ID
CREATE SEQUENCE IF NOT EXISTS usn_journal_info_id_seq START 1;

-- 创建USN日志信息表 (DuckDB版本)
CREATE TABLE IF NOT EXISTS usn_journal_info (
    id BIGINT PRIMARY KEY DEFAULT nextval('usn_journal_info_id_seq'),
    file_name VARCHAR NOT NULL,
    file_path VARCHAR NOT NULL,
    file_reference_number BIGINT NOT NULL,
    parent_file_reference_number BIGINT NOT NULL,
    usn BIGINT NOT NULL,
    time_stamp BIGINT NOT NULL,
    reason INTEGER NOT NULL,
    source_info INTEGER NOT NULL,
    file_attributes INTEGER NOT NULL,
    disk_identifier VARCHAR NOT NULL,
    scan_time VARCHAR NOT NULL
);

-- 创建索引以提高查询性能
-- USN日志表的索引设计针对USN特有的查询模式进行优化

-- 文件名索引（支持快速文件名搜索）
CREATE INDEX IF NOT EXISTS idx_usn_file_name ON usn_journal_info(file_name);

-- 文件路径索引（支持快速路径搜索）
CREATE INDEX IF NOT EXISTS idx_usn_file_path ON usn_journal_info(file_path);

-- 文件引用号索引（支持精确文件定位）
CREATE INDEX IF NOT EXISTS idx_usn_file_ref ON usn_journal_info(file_reference_number);

-- 父文件引用号索引（支持目录结构查询）
CREATE INDEX IF NOT EXISTS idx_usn_parent_ref ON usn_journal_info(parent_file_reference_number);

-- USN索引（支持按更新序列号排序和范围查询）
CREATE INDEX IF NOT EXISTS idx_usn_number ON usn_journal_info(usn);

-- 时间戳索引（支持按时间排序和范围查询）
CREATE INDEX IF NOT EXISTS idx_usn_timestamp ON usn_journal_info(time_stamp);

-- 变更原因索引（支持按变更类型过滤）
CREATE INDEX IF NOT EXISTS idx_usn_reason ON usn_journal_info(reason);

-- 磁盘标识符索引（支持按磁盘过滤）
CREATE INDEX IF NOT EXISTS idx_usn_disk_identifier ON usn_journal_info(disk_identifier);

-- 扫描时间索引（支持按扫描时间排序）
CREATE INDEX IF NOT EXISTS idx_usn_scan_time ON usn_journal_info(scan_time);

-- 复合索引优化常见查询模式

-- 磁盘和USN复合索引（优化按磁盘的USN范围查询）
CREATE INDEX IF NOT EXISTS idx_usn_disk_usn ON usn_journal_info(disk_identifier, usn);

-- 磁盘和时间戳复合索引（优化按磁盘的时间范围查询）
CREATE INDEX IF NOT EXISTS idx_usn_disk_time ON usn_journal_info(disk_identifier, time_stamp);

-- 文件引用号和磁盘复合索引（优化文件跟踪查询）
CREATE INDEX IF NOT EXISTS idx_usn_file_ref_disk ON usn_journal_info(file_reference_number, disk_identifier);

-- 变更原因和磁盘复合索引（优化按变更类型和磁盘的查询）
CREATE INDEX IF NOT EXISTS idx_usn_reason_disk ON usn_journal_info(reason, disk_identifier);

-- 时间戳和变更原因复合索引（优化时间范围内的特定变更查询）
CREATE INDEX IF NOT EXISTS idx_usn_time_reason ON usn_journal_info(time_stamp, reason);

-- 文件名和磁盘复合索引（优化文件名搜索）
CREATE INDEX IF NOT EXISTS idx_usn_file_name_disk ON usn_journal_info(file_name, disk_identifier);

-- 文件路径和磁盘复合索引（优化路径搜索）
CREATE INDEX IF NOT EXISTS idx_usn_file_path_disk ON usn_journal_info(file_path, disk_identifier);

-- USN范围查询优化索引（磁盘、USN、时间戳三元组）
CREATE INDEX IF NOT EXISTS idx_usn_range_query ON usn_journal_info(disk_identifier, usn, time_stamp);

-- 文件属性索引（支持按文件属性过滤，如目录、隐藏文件等）
CREATE INDEX IF NOT EXISTS idx_usn_file_attributes ON usn_journal_info(file_attributes);

-- 源信息索引（支持按USN源信息过滤）
CREATE INDEX IF NOT EXISTS idx_usn_source_info ON usn_journal_info(source_info);

-- DuckDB特有的优化设置
-- 禁用进度条
PRAGMA disable_progress_bar;

-- 设置线程数
SET threads = 4;

-- 设置内存限制（根据系统配置调整）
SET memory_limit = '1GB';

-- 启用对象缓存以提高性能
PRAGMA enable_object_cache;

-- 创建视图以简化常见查询

-- 文件创建事件视图
CREATE VIEW IF NOT EXISTS usn_file_created AS
SELECT 
    file_name,
    file_path,
    file_reference_number,
    time_stamp,
    disk_identifier,
    scan_time
FROM usn_journal_info 
WHERE reason & 256 = 256; -- FILE_CREATE (0x00000100)

-- 文件删除事件视图
CREATE VIEW IF NOT EXISTS usn_file_deleted AS
SELECT 
    file_name,
    file_path,
    file_reference_number,
    time_stamp,
    disk_identifier,
    scan_time
FROM usn_journal_info 
WHERE reason & 512 = 512; -- FILE_DELETE (0x00000200)

-- 文件重命名事件视图
CREATE VIEW IF NOT EXISTS usn_file_renamed AS
SELECT 
    file_name,
    file_path,
    file_reference_number,
    time_stamp,
    disk_identifier,
    scan_time
FROM usn_journal_info 
WHERE reason & 4096 = 4096 OR reason & 8192 = 8192; -- RENAME_OLD_NAME or RENAME_NEW_NAME

-- 文件数据修改事件视图
CREATE VIEW IF NOT EXISTS usn_file_data_changed AS
SELECT 
    file_name,
    file_path,
    file_reference_number,
    time_stamp,
    disk_identifier,
    scan_time
FROM usn_journal_info 
WHERE reason & 1 = 1 OR reason & 2 = 2 OR reason & 4 = 4; -- DATA_OVERWRITE, DATA_EXTEND, DATA_TRUNCATION

-- 最近活动视图（最近24小时的USN记录）
CREATE VIEW IF NOT EXISTS usn_recent_activity AS
SELECT 
    file_name,
    file_path,
    file_reference_number,
    time_stamp,
    reason,
    disk_identifier,
    scan_time
FROM usn_journal_info 
WHERE time_stamp > (SELECT MAX(time_stamp) - 864000000000 FROM usn_journal_info); -- 24小时前（100纳秒单位）

-- 创建统计信息表（可选，用于性能监控）
CREATE TABLE IF NOT EXISTS usn_scan_statistics (
    id BIGINT PRIMARY KEY DEFAULT nextval('usn_journal_info_id_seq'),
    disk_identifier VARCHAR NOT NULL,
    scan_start_time VARCHAR NOT NULL,
    scan_end_time VARCHAR,
    total_records BIGINT DEFAULT 0,
    processed_records BIGINT DEFAULT 0,
    scan_duration_ms BIGINT DEFAULT 0,
    scan_status VARCHAR DEFAULT 'running', -- running, completed, failed, stopped
    error_message VARCHAR,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 统计信息表索引
CREATE INDEX IF NOT EXISTS idx_usn_stats_disk ON usn_scan_statistics(disk_identifier);
CREATE INDEX IF NOT EXISTS idx_usn_stats_status ON usn_scan_statistics(scan_status);
CREATE INDEX IF NOT EXISTS idx_usn_stats_time ON usn_scan_statistics(scan_start_time);
