-- 创建序列用于自增ID
CREATE SEQUENCE IF NOT EXISTS file_system_info_id_seq START 1;

-- 创建文件系统信息表 (DuckDB版本)
CREATE TABLE IF NOT EXISTS file_system_info (
    id BIGINT PRIMARY KEY DEFAULT nextval('file_system_info_id_seq'),
    file_name VARCHAR NOT NULL,
    file_path VARCHAR NOT NULL,
    file_size BIGINT DEFAULT 0,
    mft_entry BIGINT NOT NULL,
    is_directory BOOLEAN DEFAULT false,
    is_deleted BOOLEAN DEFAULT false,
    created_time VARCHAR,
    modified_time VARCHAR,
    accessed_time VARCHAR,
    disk_identifier VARCHAR NOT NULL,
    scan_time VARCHAR
);

-- 创建索引以提高查询性能
-- 注意：DuckDB中索引的创建语法与SQLite略有不同

-- 文件名索引（支持快速文件名搜索）
CREATE INDEX IF NOT EXISTS idx_file_name ON file_system_info(file_name);

-- 文件路径索引（支持快速路径搜索）
CREATE INDEX IF NOT EXISTS idx_file_path ON file_system_info(file_path);

-- MFT条目和磁盘标识符复合索引（支持精确文件定位）
CREATE INDEX IF NOT EXISTS idx_mft_entry ON file_system_info(mft_entry, disk_identifier);

-- 磁盘标识符索引（支持按磁盘过滤）
CREATE INDEX IF NOT EXISTS idx_disk_identifier ON file_system_info(disk_identifier);

-- 文件类型索引（支持按目录/文件过滤）
CREATE INDEX IF NOT EXISTS idx_is_directory ON file_system_info(is_directory);

-- 删除状态索引（支持按删除状态过滤）
CREATE INDEX IF NOT EXISTS idx_is_deleted ON file_system_info(is_deleted);

-- 扫描时间索引（支持按扫描时间排序）
CREATE INDEX IF NOT EXISTS idx_scan_time ON file_system_info(scan_time);

-- 文件名和磁盘标识符复合索引（优化常见查询）
CREATE INDEX IF NOT EXISTS idx_file_name_disk ON file_system_info(file_name, disk_identifier);

-- 文件路径和磁盘标识符复合索引（优化路径查询）
CREATE INDEX IF NOT EXISTS idx_file_path_disk ON file_system_info(file_path, disk_identifier);

-- 文件大小索引（支持按大小范围查询）
CREATE INDEX IF NOT EXISTS idx_file_size ON file_system_info(file_size);

-- 时间范围查询优化索引
CREATE INDEX IF NOT EXISTS idx_time_range ON file_system_info(created_time, modified_time);

-- DuckDB特有的优化设置
-- 禁用进度条
PRAGMA disable_progress_bar;

-- 设置线程数
SET threads = 4;

-- 设置内存限制（根据系统配置调整）
SET memory_limit = '1GB';

-- 启用对象缓存以提高性能
PRAGMA enable_object_cache;
