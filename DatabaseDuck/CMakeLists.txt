project(DatabaseDuck LANGUAGES CXX)

# 创建DatabaseDuck库
qt_add_library(DatabaseDuck STATIC
    DatabaseDuck.qrc
    DatabaseManagerDuck.h DatabaseManagerDuck.cpp
    SqlFileReaderDuck.h SqlFileReaderDuck.cpp
    AsyncRepositoryBaseDuck.h AsyncRepositoryBaseDuck.cpp
)

# 链接库
target_link_libraries(DatabaseDuck
    PUBLIC
        Qt6::Core
        DuckDB
        Entity
)

# 编译器特定设置
if(MSVC)
    target_compile_definitions(DatabaseDuck PRIVATE
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
    )
    target_compile_options(DatabaseDuck PRIVATE
        /W3
        /wd4996  # 禁用已弃用函数警告
    )
endif()

# 包含目录
target_include_directories(DatabaseDuck PUBLIC
        ${CMAKE_CURRENT_SOURCE_DIR}
)

add_subdirectory(test)
