#include "AsyncRepositoryBaseDuck.h"
#include "DatabaseManagerDuck.h"
#include <QDebug>
#include <QDateTime>
#include <QCoreApplication>
#include <QMetaObject>
#include <limits>

AsyncRepositoryBaseDuck::AsyncRepositoryBaseDuck(DatabaseManagerDuck *dbManager, 
                                                 const QString &repositoryName,
                                                 QObject *parent)
    : QObject(parent), m_dbManager(dbManager), m_repositoryName(repositoryName),
      m_workerThread(nullptr), m_running(false), m_cleanupTimer(nullptr),
      m_totalOperations(0), m_successfulOperations(0), m_failedOperations(0),
      m_totalExecutionTime(0), m_maxExecutionTime(0), 
      m_minExecutionTime(std::numeric_limits<qint64>::max()) {
    
    if (!m_dbManager) {
        qWarning() << "DatabaseManagerDuck is null in AsyncRepositoryBaseDuck constructor";
    }
}

AsyncRepositoryBaseDuck::~AsyncRepositoryBaseDuck() {
    stop();
}

void AsyncRepositoryBaseDuck::start() {
    QMutexLocker locker(&m_mutex);
    
    if (m_running) {
        qDebug() << "Repository" << m_repositoryName << "is already running";
        return;
    }
    
    if (!m_dbManager) {
        qWarning() << "Cannot start repository: DatabaseManagerDuck is null";
        return;
    }
    
    if (!m_dbManager->isConnected()) {
        qWarning() << "Cannot start repository: Database is not connected";
        return;
    }
    
    initializeWorkerThread();

    // 创建清理定时器（确保在主线程中创建）
    if (QThread::currentThread() == QCoreApplication::instance()->thread()) {
        m_cleanupTimer = new QTimer(this);
        connect(m_cleanupTimer, &QTimer::timeout, this, &AsyncRepositoryBaseDuck::performPeriodicCleanup);
        m_cleanupTimer->start(60000); // 每分钟清理一次
    } else {
        // 如果不在主线程，使用QMetaObject::invokeMethod在主线程中创建
        QMetaObject::invokeMethod(this, [this]() {
            m_cleanupTimer = new QTimer(this);
            connect(m_cleanupTimer, &QTimer::timeout, this, &AsyncRepositoryBaseDuck::performPeriodicCleanup);
            m_cleanupTimer->start(60000);
        }, Qt::QueuedConnection);
    }
    
    m_running = true;
    
    qDebug() << "Repository" << m_repositoryName << "started successfully";
    emit repositoryStarted(m_repositoryName);
}

void AsyncRepositoryBaseDuck::stop() {
    QMutexLocker locker(&m_mutex);
    
    if (!m_running) {
        return;
    }
    
    m_running = false;
    
    // 停止清理定时器
    if (m_cleanupTimer) {
        m_cleanupTimer->stop();
        m_cleanupTimer->deleteLater();
        m_cleanupTimer = nullptr;
    }
    
    cleanupWorkerThread();
    
    qDebug() << "Repository" << m_repositoryName << "stopped";
    emit repositoryStopped(m_repositoryName);
}

bool AsyncRepositoryBaseDuck::isRunning() const {
    return m_running;
}

QString AsyncRepositoryBaseDuck::getRepositoryName() const {
    return m_repositoryName;
}

QString AsyncRepositoryBaseDuck::getStatistics() const {
    qint64 totalOps = m_totalOperations.load();
    qint64 successOps = m_successfulOperations.load();
    qint64 failedOps = m_failedOperations.load();
    qint64 totalTime = m_totalExecutionTime.load();
    qint64 maxTime = m_maxExecutionTime.load();
    qint64 minTime = m_minExecutionTime.load();
    
    double successRate = totalOps > 0 ? (double)successOps / totalOps * 100.0 : 0.0;
    double avgTime = totalOps > 0 ? (double)totalTime / totalOps : 0.0;
    
    return QString("Repository Statistics [%1]:\n"
                  "  Total Operations: %2\n"
                  "  Successful: %3 (%.1f%%)\n"
                  "  Failed: %4\n"
                  "  Total Execution Time: %5ms\n"
                  "  Average Execution Time: %.2fms\n"
                  "  Max Execution Time: %6ms\n"
                  "  Min Execution Time: %7ms\n"
                  "  Running: %8")
           .arg(m_repositoryName)
           .arg(totalOps)
           .arg(successOps).arg(successRate)
           .arg(failedOps)
           .arg(totalTime)
           .arg(avgTime)
           .arg(maxTime)
           .arg(minTime == std::numeric_limits<qint64>::max() ? 0 : minTime)
           .arg(m_running ? "Yes" : "No");
}

void AsyncRepositoryBaseDuck::resetStatistics() {
    QMutexLocker locker(&m_mutex);
    
    m_totalOperations = 0;
    m_successfulOperations = 0;
    m_failedOperations = 0;
    m_totalExecutionTime = 0;
    m_maxExecutionTime = 0;
    m_minExecutionTime = std::numeric_limits<qint64>::max();
    
    qDebug() << "Statistics reset for repository" << m_repositoryName;
}

duckdb_connection AsyncRepositoryBaseDuck::getThreadSafeConnection() {
    if (!m_dbManager) {
        qWarning() << "DatabaseManagerDuck is null";
        return nullptr;
    }

    return m_dbManager->getConnection();
}

void AsyncRepositoryBaseDuck::releaseConnection(duckdb_connection connection) {
    if (!m_dbManager || !connection) {
        return;
    }

    m_dbManager->releaseConnection(connection);
}

DatabaseManagerDuck* AsyncRepositoryBaseDuck::getDatabaseManager() const {
    return m_dbManager;
}

void AsyncRepositoryBaseDuck::recordStatistics(const QString &operationName, bool success, qint64 executionTime) {
    m_totalOperations++;
    
    if (success) {
        m_successfulOperations++;
    } else {
        m_failedOperations++;
    }
    
    m_totalExecutionTime += executionTime;
    updateExecutionTimeStats(executionTime);
    
    // 大幅减少详细日志输出
    #ifdef QT_DEBUG
    // 只在失败或超长时间操作时输出日志
    if (!success || executionTime > 1000) {
        qDebug() << QString("Operation [%1] %2: %3ms")
                    .arg(operationName)
                    .arg(success ? "SUCCESS" : "FAILED")
                    .arg(executionTime);
    }
    #endif
}

bool AsyncRepositoryBaseDuck::handleDatabaseError(const QString &error, const QString &operationName) {
    Q_UNUSED(operationName)
    
    // 检查是否为可重试的错误
    QStringList retryableErrors = {
        "database is locked",
        "connection lost",
        "timeout",
        "busy"
    };
    
    QString lowerError = error.toLower();
    for (const QString &retryableError : retryableErrors) {
        if (lowerError.contains(retryableError)) {
            qDebug() << "Retryable error detected:" << error;
            return true;
        }
    }
    
    return false;
}

void AsyncRepositoryBaseDuck::performPeriodicCleanup() {
    if (!m_running) {
        return;
    }
    
    // 执行定期清理任务
    qDebug() << "Performing periodic cleanup for repository" << m_repositoryName;
    
    // 这里可以添加具体的清理逻辑，比如：
    // - 清理过期的缓存
    // - 释放未使用的资源
    // - 记录统计信息
    
    // 记录当前统计信息（可选）
    #ifdef QT_DEBUG
    if (m_totalOperations > 0 && m_totalOperations % 1000 == 0) {
        qDebug() << getStatistics();
    }
    #endif
}

void AsyncRepositoryBaseDuck::initializeWorkerThread() {
    if (m_workerThread) {
        cleanupWorkerThread();
    }

    // 创建工作线程但不移动Repository对象到线程
    // Repository对象保持在主线程，只有具体的数据库操作在工作线程中执行
    m_workerThread = new QThread();
    m_workerThread->start();

    qDebug() << "Worker thread initialized for repository" << m_repositoryName;
}

void AsyncRepositoryBaseDuck::cleanupWorkerThread() {
    if (!m_workerThread) {
        return;
    }

    // 停止并等待线程结束
    m_workerThread->quit();
    if (!m_workerThread->wait(5000)) {
        qWarning() << "Worker thread did not finish within timeout, terminating";
        m_workerThread->terminate();
        m_workerThread->wait(1000);
    }

    m_workerThread->deleteLater();
    m_workerThread = nullptr;

    qDebug() << "Worker thread cleaned up for repository" << m_repositoryName;
}

void AsyncRepositoryBaseDuck::updateExecutionTimeStats(qint64 executionTime) {
    // 更新最大执行时间
    qint64 currentMax = m_maxExecutionTime.load();
    while (executionTime > currentMax && 
           !m_maxExecutionTime.compare_exchange_weak(currentMax, executionTime)) {
        // 继续尝试直到成功更新
    }
    
    // 更新最小执行时间
    qint64 currentMin = m_minExecutionTime.load();
    while (executionTime < currentMin && 
           !m_minExecutionTime.compare_exchange_weak(currentMin, executionTime)) {
        // 继续尝试直到成功更新
    }
}
