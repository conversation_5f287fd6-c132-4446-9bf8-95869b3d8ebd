cmake_minimum_required(VERSION 3.16)

project(DatabaseManagerDuckTest LANGUAGES CXX)

enable_testing()

find_package(Qt6 REQUIRED COMPONENTS Test Core)

qt_add_executable(DatabaseManagerDuckTest
        tst_databasemanagerduck.cpp
)

add_test(NAME DatabaseManagerDuckTest COMMAND DatabaseManagerDuckTest)

target_link_libraries(DatabaseManagerDuckTest
        PRIVATE
        Qt6::Test
        Qt6::Core
        DatabaseDuck
        DuckDB
        Entity
)

set_tests_properties(DatabaseManagerDuckTest PROPERTIES
    TIMEOUT 300
    LABELS "DatabaseDuck;Manager"
)
