#ifndef DATABASEMANAGERDUCK_H
#define DATABASEMANAGERDUCK_H

#include <QObject>
#include <QString>
#include <QMutex>
#include <QMutexLocker>
#include <QThread>
#include <memory>
#include <functional>
#include <vector>
#include <queue>
#include <atomic>
#include "duckdb.h"

/**
 * @brief DuckDB数据库管理器类
 *
 * 基于DuckDB的数据库管理器，提供线程安全的数据库操作
 * 支持连接池、事务管理、SQL文件执行等功能
 * 针对分析型工作负载进行优化
 */
class DatabaseManagerDuck : public QObject {
    Q_OBJECT
    friend class DatabaseManagerDuckTest;

public:
    /**
     * @brief 构造函数
     * @param parent 父对象
     */
    explicit DatabaseManagerDuck(QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~DatabaseManagerDuck() override;

    /**
     * @brief 初始化数据库
     * @param dbPath 数据库文件路径
     * @param maxConnections 最大连接数
     * @return 成功返回true
     */
    bool initialize(const QString &dbPath, int maxConnections = 10);

    /**
     * @brief 检查数据库是否已连接
     * @return 已连接返回true
     */
    bool isConnected() const;

    /**
     * @brief 获取数据库连接
     * @return DuckDB连接句柄
     */
    duckdb_connection getConnection();

    /**
     * @brief 释放数据库连接
     * @param connection 要释放的连接
     */
    void releaseConnection(duckdb_connection connection);

    /**
     * @brief 执行SQL语句
     * @param sql SQL语句
     * @param callback 结果回调函数
     */
    void executeSQL(const QString &sql, std::function<void(bool success, const QString &error)> callback = nullptr);

    /**
     * @brief 执行SQL文件中的语句
     * @param sqlFilePath SQL文件路径
     * @return 成功返回true，失败返回false
     */
    bool executeSqlFile(const QString &sqlFilePath);

    /**
     * @brief 创建所有数据表
     * @param dbPath 数据库路径（用于信号发射）
     */
    void createTables(const QString &dbPath);

    /**
     * @brief 开始事务
     * @param connection 数据库连接
     * @return 成功返回true
     */
    bool beginTransaction(duckdb_connection connection);

    /**
     * @brief 提交事务
     * @param connection 数据库连接
     * @return 成功返回true
     */
    bool commitTransaction(duckdb_connection connection);

    /**
     * @brief 回滚事务
     * @param connection 数据库连接
     * @return 成功返回true
     */
    bool rollbackTransaction(duckdb_connection connection);

    /**
     * @brief 执行查询并返回结果
     * @param sql SQL查询语句
     * @param connection 数据库连接
     * @param out_result 输出结果
     * @return 成功返回DuckDBSuccess
     */
    duckdb_state executeQuery(const QString &sql, duckdb_connection connection, duckdb_result *out_result);

    /**
     * @brief 创建Appender用于批量插入
     * @param connection 数据库连接
     * @param table_name 表名
     * @param out_appender 输出Appender
     * @return 成功返回DuckDBSuccess
     */
    duckdb_state createAppender(duckdb_connection connection, const QString &table_name, duckdb_appender *out_appender);

    /**
     * @brief 使用Appender执行批量插入操作（最佳实践）
     * @param appender Appender对象
     * @param values 批量数据
     * @return 成功返回true
     */
    bool executeBatchInsertWithAppender(duckdb_appender appender, const std::vector<std::vector<QString>>& values);

    /**
     * @brief 优化数据库性能设置
     */
    void optimizeDatabasePerformance();

    /**
     * @brief 获取当前线程ID（用于调试）
     * @return 当前线程ID
     */
    Qt::HANDLE getCurrentThreadId() const;

    /**
     * @brief 检查表是否已创建
     * @return 已创建返回true
     */
    bool isTablesCreated() const;

    /**
     * @brief 检查数据库是否就绪（已初始化且已连接）
     * @return 就绪返回true
     */
    bool isDatabaseReady() const;

    /**
     * @brief 获取数据库统计信息
     * @return 统计信息字符串
     */
    QString getDatabaseStatistics() const;

signals:
    /**
     * @brief 数据库初始化完成信号
     * @param success 是否成功
     * @param error 错误信息（成功时为空）
     */
    void databaseInitialized(bool success, const QString &error);

    /**
     * @brief 数据表创建完成信号
     * @param success 是否成功
     * @param error 错误信息（成功时为空）
     * @param dbPath 数据库路径
     */
    void tablesCreated(bool success, const QString &error, const QString &dbPath);

    /**
     * @brief 数据库连接状态变化信号
     * @param connected 是否已连接
     */
    void connectionStatusChanged(bool connected);

private:
    duckdb_database m_database;                       ///< DuckDB数据库实例
    std::queue<duckdb_connection> m_connectionPool;   ///< 连接池
    QString m_dbPath;                                  ///< 数据库文件路径
    bool m_initialized;                                ///< 是否已初始化
    bool m_tablesCreated;                              ///< 是否已创建表
    mutable QMutex m_mutex;                            ///< 线程安全保护
    mutable QMutex m_poolMutex;                        ///< 连接池保护
    int m_maxConnections;                              ///< 最大连接数
    std::atomic<int> m_activeConnections;              ///< 活跃连接数

    /**
     * @brief 初始化连接池
     */
    void initializeConnectionPool();

    /**
     * @brief 创建新的数据库连接
     * @return 数据库连接句柄
     */
    duckdb_connection createConnection();

    /**
     * @brief 验证数据库文件权限
     * @return 权限验证通过返回true
     */
    bool validateFilePermissions() const;

    /**
     * @brief 配置数据库性能选项
     * @param connection 数据库连接
     */
    void configureDatabaseOptions(duckdb_connection connection);
};

#endif // DATABASEMANAGERDUCK_H
