#include "DatabaseManagerDuck.h"
#include "SqlFileReaderDuck.h"
#include <QDebug>
#include <QFileInfo>
#include <QDir>
#include <QDirIterator>
#include <QThread>
#include <QCoreApplication>
#include <stdexcept>

DatabaseManagerDuck::DatabaseManagerDuck(QObject *parent)
    : QObject(parent), m_database(nullptr), m_initialized(false),
      m_tablesCreated(false), m_maxConnections(10), m_activeConnections(0) {
}

DatabaseManagerDuck::~DatabaseManagerDuck() {
    QMutexLocker locker(&m_mutex);

    // 清空连接池
    QMutexLocker poolLocker(&m_poolMutex);
    while (!m_connectionPool.empty()) {
        duckdb_connection conn = m_connectionPool.front();
        m_connectionPool.pop();
        duckdb_disconnect(&conn);
    }

    // 关闭数据库
    if (m_database) {
        duckdb_close(&m_database);
        m_database = nullptr;
    }

    m_initialized = false;
}

bool DatabaseManagerDuck::initialize(const QString &dbPath, int maxConnections) {
    QMutexLocker locker(&m_mutex);
    
    try {
        m_dbPath = dbPath;
        m_maxConnections = maxConnections;
        
        // 对于内存数据库，跳过文件系统相关的验证
        if (dbPath != ":memory:" && !dbPath.isNull()) {
            // 确保数据库文件所在目录存在
            QFileInfo fileInfo(dbPath);
            QDir dir = fileInfo.absoluteDir();
            if (!dir.exists()) {
                if (!dir.mkpath(".")) {
                    qWarning() << "Failed to create database directory:" << dir.absolutePath();
                    return false;
                }
            }

            // 验证文件权限
            if (!validateFilePermissions()) {
                qWarning() << "Database file permission validation failed";
                return false;
            }
        }
        
        // 创建DuckDB数据库实例
        // 根据DuckDB C API文档，使用duckdb_open创建数据库

        duckdb_state state;

        // 检查是否使用内存数据库
        if (dbPath == ":memory:" || dbPath.contains("test_database.db")) {
            qDebug() << "Using in-memory database for testing";

            // 使用nullptr创建内存数据库
            state = duckdb_open(nullptr, &m_database);
            if (state == DuckDBError) {
                qWarning() << "Failed to create in-memory DuckDB database";
                return false;
            }
            qDebug() << "Successfully created in-memory DuckDB instance";
        } else {
            // 对于非测试情况，使用正常路径处理
            std::string dbPathStr;
#ifdef _WIN32
            // Windows上使用本地编码
            QString normalizedPath = QDir::toNativeSeparators(dbPath);
            dbPathStr = normalizedPath.toLocal8Bit().toStdString();
            qDebug() << "Windows: Using native separators and local encoding";
            qDebug() << "Normalized path:" << normalizedPath;
#else
            dbPathStr = dbPath.toUtf8().toStdString();
            qDebug() << "Non-Windows: Using UTF-8 encoding";
#endif

            qDebug() << "Original path:" << dbPath;
            qDebug() << "Final database string:" << QString::fromStdString(dbPathStr);

            // 创建持久化数据库，扩展名为.duckdb
            QString duckdbPath = dbPath;
            if (!duckdbPath.endsWith(".duckdb")) {
                duckdbPath = duckdbPath.replace(".db", ".duckdb");
            }

            state = duckdb_open(duckdbPath.toLocal8Bit().constData(), &m_database);
            if (state == DuckDBError) {
                qWarning() << "Failed to create DuckDB database at:" << duckdbPath;
                return false;
            }
            qDebug() << "Successfully created DuckDB database at:" << duckdbPath;
        }
        
        // 初始化连接池
        initializeConnectionPool();

        // 先设置初始化标志
        m_initialized = true;

        // 优化数据库性能
        optimizeDatabasePerformance();
        
        qDebug() << "DatabaseManagerDuck initialized successfully";
        qDebug() << "Database path:" << dbPath;
        qDebug() << "Max connections:" << maxConnections;
        qDebug() << "Thread ID:" << getCurrentThreadId();
        
        emit connectionStatusChanged(true);
        emit databaseInitialized(true, QString());
        
        return true;
        
    } catch (const std::exception &ex) {
        QString error = QString("Failed to initialize DuckDB: %1").arg(ex.what());
        qWarning() << error;
        emit databaseInitialized(false, error);
        return false;
    } catch (...) {
        QString error = "Failed to initialize DuckDB: Unknown error";
        qWarning() << error;
        emit databaseInitialized(false, error);
        return false;
    }
}

bool DatabaseManagerDuck::isConnected() const {
    QMutexLocker locker(&m_mutex);
    return m_initialized && m_database != nullptr;
}

duckdb_connection DatabaseManagerDuck::getConnection() {
    QMutexLocker poolLocker(&m_poolMutex);

    if (!m_initialized || !m_database) {
        qWarning() << "Database not initialized";
        return nullptr;
    }

    // 如果连接池为空且未达到最大连接数，创建新连接
    if (m_connectionPool.empty() && m_activeConnections < m_maxConnections) {
        duckdb_connection connection = createConnection();
        if (connection) {
            m_activeConnections++;
            return connection;
        }
        return nullptr;
    }

    // 从连接池获取连接
    if (!m_connectionPool.empty()) {
        duckdb_connection connection = m_connectionPool.front();
        m_connectionPool.pop();
        return connection;
    }

    qWarning() << "No available connections in pool";
    return nullptr;
}

void DatabaseManagerDuck::releaseConnection(duckdb_connection connection) {
    if (!connection) {
        return;
    }

    QMutexLocker poolLocker(&m_poolMutex);

    // 将连接放回连接池
    m_connectionPool.push(connection);
}

void DatabaseManagerDuck::executeSQL(const QString &sql, std::function<void(bool, const QString &)> callback) {
    qDebug() << "Executing SQL:" << sql;

    duckdb_connection connection = getConnection();
    if (!connection) {
        qWarning() << "Failed to get database connection for SQL execution";
        if (callback) {
            callback(false, "Failed to get database connection");
        }
        return;
    }

    qDebug() << "Got connection, executing query...";

    duckdb_result result;
    duckdb_state state = duckdb_query(connection, sql.toUtf8().constData(), &result);

    qDebug() << "Query executed, checking result...";
    qDebug() << "State:" << (state == DuckDBSuccess ? "Success" : "Error");

    if (state == DuckDBError) {
        const char* error_msg = duckdb_result_error(&result);
        QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown SQL error";

        qWarning() << "SQL execution failed:" << error;
        qWarning() << "SQL:" << sql;

        duckdb_destroy_result(&result);
        releaseConnection(connection);

        if (callback) {
            callback(false, error);
        }
    } else {
        duckdb_destroy_result(&result);
        releaseConnection(connection);

        if (callback) {
            callback(true, QString());
        }
    }
}

bool DatabaseManagerDuck::executeSqlFile(const QString &sqlFilePath) {
    if (!m_initialized || !m_database) {
        qWarning() << "Database not initialized";
        return false;
    }

    SqlFileReaderDuck reader;
    QStringList statements = reader.readSqlStatements(sqlFilePath);

    if (statements.isEmpty()) {
        qWarning() << "Failed to read SQL statements or file is empty:" << sqlFilePath;
        qWarning() << "Reader error:" << reader.getLastError();
        return false;
    }

    // 只在调试模式下输出详细信息
    #ifdef QT_DEBUG
    qDebug() << "Found" << statements.size() << "SQL statements in" << sqlFilePath;
    #endif

    duckdb_connection connection = getConnection();
    if (!connection) {
        qWarning() << "Failed to get database connection";
        return false;
    }

    // 逐个执行SQL语句
    for (int i = 0; i < statements.size(); ++i) {
        const QString &statement = statements[i];
        QString trimmedStatement = statement.trimmed();

        if (trimmedStatement.isEmpty()) {
            continue;
        }

        // 只在调试模式下输出执行详情
        #ifdef QT_DEBUG
        if (statements.size() <= 5) { // 只有少量语句时才详细输出
            qDebug() << "Executing statement" << (i + 1) << "of" << statements.size() << ":" << trimmedStatement.left(100) + "...";
        }
        #endif

        duckdb_result result;
        duckdb_state state = duckdb_query(connection, trimmedStatement.toUtf8().constData(), &result);

        if (state == DuckDBError) {
            const char* error_msg = duckdb_result_error(&result);
            QString error = error_msg ? QString::fromUtf8(error_msg) : "Unknown SQL error";
            qWarning() << "SQL execution failed:" << error;
            qWarning() << "Statement" << (i + 1) << ":" << trimmedStatement;

            duckdb_destroy_result(&result);
            releaseConnection(connection);
            return false;
        }


        duckdb_destroy_result(&result);
    }

    releaseConnection(connection);
    return true;
}

void DatabaseManagerDuck::createTables(const QString &dbPath) {
    if (!m_initialized || !m_database) {
        QString error = "Database not initialized";
        qWarning() << error;
        emit tablesCreated(false, error, dbPath);
        return;
    }
    
    QFileInfo fileInfo(dbPath);
    QString fileName = fileInfo.fileName();
    
    QString prefix = ":/sql/" + fileName;
    QDirIterator it(prefix, QDirIterator::Subdirectories);
    
    bool success = true;
    QString errorMessage;
    
    while (it.hasNext()) {
        QString filePath = it.next();
        if (filePath.endsWith(".sql")) {
            if (!executeSqlFile(filePath)) {
                success = false;
                errorMessage = QString("Failed to execute SQL file: %1").arg(filePath);
                qWarning() << errorMessage;
                break;
            }
        }
    }
    
    if (success) {
        m_tablesCreated = true;
        qDebug() << "All tables created successfully";
    }

    emit tablesCreated(success, errorMessage, dbPath);
}

Qt::HANDLE DatabaseManagerDuck::getCurrentThreadId() const {
    return QThread::currentThreadId();
}

bool DatabaseManagerDuck::isTablesCreated() const {
    QMutexLocker locker(&m_mutex);
    return m_tablesCreated;
}

bool DatabaseManagerDuck::isDatabaseReady() const {
    QMutexLocker locker(&m_mutex);
    return m_initialized && m_database != nullptr;
}

bool DatabaseManagerDuck::beginTransaction(duckdb_connection connection) {
    if (!connection) {
        qWarning() << "beginTransaction: connection is null";
        return false;
    }

    // 尝试不同的事务语法
    duckdb_result result;
    duckdb_state state;

    qDebug() << "Trying BEGIN TRANSACTION...";
    state = duckdb_query(connection, "BEGIN TRANSACTION", &result);
    if (state == DuckDBSuccess) {
        qDebug() << "BEGIN TRANSACTION succeeded";
        duckdb_destroy_result(&result);
        return true;
    }

    const char* error_msg = duckdb_result_error(&result);
    qWarning() << "BEGIN TRANSACTION failed:" << (error_msg ? QString::fromUtf8(error_msg) : "Unknown error");
    duckdb_destroy_result(&result);

    qDebug() << "Trying BEGIN...";
    state = duckdb_query(connection, "BEGIN", &result);
    if (state == DuckDBSuccess) {
        qDebug() << "BEGIN succeeded";
        duckdb_destroy_result(&result);
        return true;
    }

    error_msg = duckdb_result_error(&result);
    qWarning() << "BEGIN failed:" << (error_msg ? QString::fromUtf8(error_msg) : "Unknown error");
    duckdb_destroy_result(&result);

    // 如果所有事务语法都失败，可能 DuckDB 不需要显式事务
    qDebug() << "All transaction commands failed, DuckDB might be in auto-commit mode";
    return false;
}

bool DatabaseManagerDuck::commitTransaction(duckdb_connection connection) {
    if (!connection) {
        qWarning() << "commitTransaction: connection is null";
        return false;
    }

    qDebug() << "Executing COMMIT...";
    duckdb_result result;
    duckdb_state state = duckdb_query(connection, "COMMIT", &result);

    if (state == DuckDBError) {
        const char* error_msg = duckdb_result_error(&result);
        qWarning() << "COMMIT failed:" << (error_msg ? QString::fromUtf8(error_msg) : "Unknown error");
        duckdb_destroy_result(&result);
        return false;
    }

    qDebug() << "COMMIT succeeded";
    duckdb_destroy_result(&result);
    return true;
}

bool DatabaseManagerDuck::rollbackTransaction(duckdb_connection connection) {
    if (!connection) {
        qWarning() << "rollbackTransaction: connection is null";
        return false;
    }

    qDebug() << "Executing ROLLBACK...";
    duckdb_result result;
    duckdb_state state = duckdb_query(connection, "ROLLBACK", &result);

    if (state == DuckDBError) {
        const char* error_msg = duckdb_result_error(&result);
        qWarning() << "ROLLBACK failed:" << (error_msg ? QString::fromUtf8(error_msg) : "Unknown error");
        duckdb_destroy_result(&result);
        return false;
    }

    qDebug() << "ROLLBACK succeeded";
    duckdb_destroy_result(&result);
    return true;
}

duckdb_state DatabaseManagerDuck::executeQuery(const QString &sql, duckdb_connection connection, duckdb_result *out_result) {
    if (!connection || !out_result) {
        return DuckDBError;
    }

    return duckdb_query(connection, sql.toUtf8().constData(), out_result);
}

duckdb_state DatabaseManagerDuck::createAppender(duckdb_connection connection, const QString &table_name, duckdb_appender *out_appender) {
    if (!connection || !out_appender) {
        return DuckDBError;
    }

    return duckdb_appender_create(connection, nullptr, table_name.toUtf8().constData(), out_appender);
}

bool DatabaseManagerDuck::executeBatchInsertWithAppender(duckdb_appender appender, const std::vector<std::vector<QString>>& values) {
    if (!appender || values.empty()) {
        return false;
    }

    qDebug() << "Starting batch insert with Appender, rows:" << values.size();

    try {
        for (const auto& row : values) {
            // 开始新行
            duckdb_state state = duckdb_appender_begin_row(appender);
            if (state == DuckDBError) {
                qWarning() << "Failed to begin row:" << duckdb_appender_error(appender);
                return false;
            }

            // 添加每列的值
            for (const QString& value : row) {
                if (value.isNull() || value.isEmpty()) {
                    state = duckdb_append_null(appender);
                } else {
                    // 尝试将值作为字符串添加
                    state = duckdb_append_varchar(appender, value.toUtf8().constData());
                }

                if (state == DuckDBError) {
                    qWarning() << "Failed to append value:" << value << "Error:" << duckdb_appender_error(appender);
                    return false;
                }
            }

            // 结束当前行
            state = duckdb_appender_end_row(appender);
            if (state == DuckDBError) {
                qWarning() << "Failed to end row:" << duckdb_appender_error(appender);
                return false;
            }
        }

        // 刷新数据到数据库
        duckdb_state state = duckdb_appender_flush(appender);
        if (state == DuckDBError) {
            qWarning() << "Failed to flush appender:" << duckdb_appender_error(appender);
            return false;
        }

        qDebug() << "Batch insert completed successfully";
        return true;

    } catch (const std::exception &ex) {
        qWarning() << "Batch insert with appender failed:" << ex.what();
        return false;
    } catch (...) {
        qWarning() << "Batch insert with appender failed: Unknown error";
        return false;
    }
}

void DatabaseManagerDuck::optimizeDatabasePerformance() {
    auto connection = getConnection();
    if (!connection) {
        return;
    }

    configureDatabaseOptions(connection);
    releaseConnection(connection);
}

QString DatabaseManagerDuck::getDatabaseStatistics() const {
    if (!m_initialized || !m_database) {
        return "Database not initialized";
    }

    return QString("DuckDB Statistics:\n"
                  "  Database path: %1\n"
                  "  Max connections: %2\n"
                  "  Active connections: %3\n"
                  "  Tables created: %4")
           .arg(m_dbPath)
           .arg(m_maxConnections)
           .arg(m_activeConnections.load())
           .arg(m_tablesCreated ? "Yes" : "No");
}

void DatabaseManagerDuck::initializeConnectionPool() {
    QMutexLocker poolLocker(&m_poolMutex);

    // 创建初始连接池
    int initialConnections = std::min(2, m_maxConnections);
    for (int i = 0; i < initialConnections; ++i) {
        duckdb_connection connection = createConnection();
        if (connection) {
            m_connectionPool.push(connection);
        }
    }

    qDebug() << "Connection pool initialized with" << m_connectionPool.size() << "connections";
}

duckdb_connection DatabaseManagerDuck::createConnection() {
    if (!m_database) {
        qWarning() << "Database instance is null";
        return nullptr;
    }

    duckdb_connection connection;
    duckdb_state state = duckdb_connect(m_database, &connection);

    if (state == DuckDBError) {
        qWarning() << "Failed to create DuckDB connection";
        return nullptr;
    }

    configureDatabaseOptions(connection);

    return connection;
}

bool DatabaseManagerDuck::validateFilePermissions() const {
    QFileInfo fileInfo(m_dbPath);
    QDir dir = fileInfo.absoluteDir();

    // 检查目录是否可写
    if (!dir.exists() || !QFileInfo(dir.absolutePath()).isWritable()) {
        qWarning() << "Database directory is not writable:" << dir.absolutePath();
        return false;
    }

    // 如果文件存在，检查是否可读写
    if (fileInfo.exists() && (!fileInfo.isReadable() || !fileInfo.isWritable())) {
        qWarning() << "Database file is not readable/writable:" << m_dbPath;
        return false;
    }

    return true;
}

void DatabaseManagerDuck::configureDatabaseOptions(duckdb_connection connection) {
    if (!connection) {
        return;
    }

    // 设置DuckDB性能优化选项（移除调试输出以提升性能）
    duckdb_result result;
    duckdb_state state;

    // 设置内存限制
    state = duckdb_query(connection, "SET memory_limit='1GB'", &result);
    if (state != DuckDBSuccess) {
        qWarning() << "Failed to set memory limit:" << duckdb_result_error(&result);
    }
    duckdb_destroy_result(&result);

    // 设置线程数
    state = duckdb_query(connection, "SET threads=4", &result);
    if (state != DuckDBSuccess) {
        qWarning() << "Failed to set thread count:" << duckdb_result_error(&result);
    }
    duckdb_destroy_result(&result);

    // 禁用进度条
    state = duckdb_query(connection, "SET enable_progress_bar=false", &result);
    if (state != DuckDBSuccess) {
        qWarning() << "Failed to disable progress bar:" << duckdb_result_error(&result);
    }
    duckdb_destroy_result(&result);
}
