#include "SqlFileReaderDuck.h"
#include <QFile>
#include <QTextStream>
#include <QFileInfo>
#include <QDebug>
#include <QRegularExpression>
#include <QStringConverter>

SqlFileReaderDuck::SqlFileReaderDuck(QObject *parent)
    : QObject(parent) {
    Q_INIT_RESOURCE(DatabaseDuck);
}

SqlFileReaderDuck::~SqlFileReaderDuck() {
}

QString SqlFileReaderDuck::readSqlFile(const QString &filePath) {
    m_lastError.clear();
    
    if (filePath.isEmpty()) {
        setLastError("File path is empty");
        return QString();
    }
    
    QString content;
    
    if (isResourcePath(filePath)) {
        content = readResourceFile(filePath);
    } else {
        content = readLocalFile(filePath);
    }
    
    if (!content.isEmpty()) {
        emit sqlFileRead(filePath, true, content, QString());
    } else {
        emit sqlFileRead(filePath, false, QString(), m_lastError);
    }
    
    return content;
}

QStringList SqlFileReaderDuck::readSqlStatements(const QString &filePath) {
    QString content = readSqlFile(filePath);
    if (content.isEmpty()) {
        return QStringList();
    }
    
    QStringList statements = parseSqlStatements(content);
    emit sqlStatementsParsed(statements, statements.size());
    
    return statements;
}

QStringList SqlFileReaderDuck::parseSqlStatements(const QString &sqlContent) {
    if (sqlContent.isEmpty()) {
        return QStringList();
    }
    
    // 去除注释
    QString cleanContent = removeComments(sqlContent);
    
    // 分割语句
    QStringList statements = splitStatements(cleanContent);
    
    // 清理每个语句
    QStringList cleanStatements;
    for (const QString &statement : statements) {
        QString cleanStatement = cleanSqlStatement(statement);
        if (!cleanStatement.isEmpty()) {
            cleanStatements.append(cleanStatement);
        }
    }
    
    return cleanStatements;
}

bool SqlFileReaderDuck::validateSqlFile(const QString &filePath) {
    if (filePath.isEmpty()) {
        setLastError("File path is empty");
        return false;
    }
    
    if (isResourcePath(filePath)) {
        QFile file(filePath);
        if (!file.exists()) {
            setLastError(QString("Resource file does not exist: %1").arg(filePath));
            return false;
        }
        return true;
    } else {
        QFileInfo fileInfo(filePath);
        if (!fileInfo.exists()) {
            setLastError(QString("File does not exist: %1").arg(filePath));
            return false;
        }
        if (!fileInfo.isReadable()) {
            setLastError(QString("File is not readable: %1").arg(filePath));
            return false;
        }
        return true;
    }
}

QString SqlFileReaderDuck::detectFileEncoding(const QString &filePath) {
    if (!validateSqlFile(filePath)) {
        return "UTF-8"; // 默认编码
    }
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return "UTF-8";
    }
    
    // 读取文件前几个字节来检测编码
    QByteArray data = file.read(1024);
    file.close();
    
    // 简单的编码检测逻辑
    if (data.startsWith("\xFF\xFE")) {
        return "UTF-16LE";
    } else if (data.startsWith("\xFE\xFF")) {
        return "UTF-16BE";
    } else if (data.startsWith("\xEF\xBB\xBF")) {
        return "UTF-8";
    }
    
    return "UTF-8"; // 默认返回UTF-8
}

QString SqlFileReaderDuck::cleanSqlStatement(const QString &sql) {
    QString cleaned = sql.trimmed();
    
    // 去除多余的空白字符
    cleaned = cleaned.replace(QRegularExpression("\\s+"), " ");
    
    // 确保语句以分号结尾（如果不是空语句）
    if (!cleaned.isEmpty() && !cleaned.endsWith(';')) {
        cleaned += ';';
    }
    
    return cleaned;
}

bool SqlFileReaderDuck::isDDLStatement(const QString &sql) {
    QString upperSql = sql.trimmed().toUpper();
    
    QStringList ddlKeywords = {
        "CREATE", "ALTER", "DROP", "TRUNCATE", "COMMENT"
    };
    
    for (const QString &keyword : ddlKeywords) {
        if (upperSql.startsWith(keyword + " ")) {
            return true;
        }
    }
    
    return false;
}

bool SqlFileReaderDuck::isDMLStatement(const QString &sql) {
    QString upperSql = sql.trimmed().toUpper();
    
    QStringList dmlKeywords = {
        "SELECT", "INSERT", "UPDATE", "DELETE", "WITH"
    };
    
    for (const QString &keyword : dmlKeywords) {
        if (upperSql.startsWith(keyword + " ")) {
            return true;
        }
    }
    
    return false;
}

QString SqlFileReaderDuck::getLastError() const {
    return m_lastError;
}

QString SqlFileReaderDuck::readResourceFile(const QString &resourcePath) {
    QFile file(resourcePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        setLastError(QString("Failed to open resource file: %1").arg(resourcePath));
        return QString();
    }
    
    QTextStream in(&file);
    in.setEncoding(QStringConverter::Utf8);
    QString content = in.readAll();
    file.close();
    
    if (content.isEmpty()) {
        setLastError(QString("Resource file is empty: %1").arg(resourcePath));
    }
    
    return content;
}

QString SqlFileReaderDuck::readLocalFile(const QString &localPath) {
    if (!validateSqlFile(localPath)) {
        return QString();
    }
    
    QFile file(localPath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        setLastError(QString("Failed to open file: %1").arg(localPath));
        return QString();
    }
    
    // 检测文件编码
    QString encoding = detectFileEncoding(localPath);
    
    QTextStream in(&file);
    if (encoding == "UTF-8") {
        in.setEncoding(QStringConverter::Utf8);
    } else if (encoding == "UTF-16LE") {
        in.setEncoding(QStringConverter::Utf16LE);
    } else if (encoding == "UTF-16BE") {
        in.setEncoding(QStringConverter::Utf16BE);
    } else {
        in.setEncoding(QStringConverter::Utf8); // 默认
    }
    
    QString content = in.readAll();
    file.close();
    
    if (content.isEmpty()) {
        setLastError(QString("File is empty: %1").arg(localPath));
    }
    
    return content;
}

QString SqlFileReaderDuck::removeComments(const QString &sql) {
    QString result = sql;
    
    // 去除单行注释 (-- 注释)
    result = result.replace(QRegularExpression("--[^\r\n]*"), "");
    
    // 去除多行注释 (/* 注释 */)
    result = result.replace(QRegularExpression("/\\*.*?\\*/", QRegularExpression::DotMatchesEverythingOption), "");
    
    return result;
}

QStringList SqlFileReaderDuck::splitStatements(const QString &sql) {
    QStringList statements;
    QString currentStatement;
    bool inString = false;
    bool inDoubleQuotes = false;
    QChar prevChar;
    
    for (int i = 0; i < sql.length(); ++i) {
        QChar currentChar = sql[i];
        
        // 处理字符串字面量
        if (currentChar == '\'' && prevChar != '\\' && !inDoubleQuotes) {
            inString = !inString;
        } else if (currentChar == '"' && prevChar != '\\' && !inString) {
            inDoubleQuotes = !inDoubleQuotes;
        }
        
        // 如果不在字符串中，检查分号
        if (!inString && !inDoubleQuotes && currentChar == ';') {
            currentStatement += currentChar;
            QString trimmedStatement = currentStatement.trimmed();
            if (!trimmedStatement.isEmpty()) {
                statements.append(trimmedStatement);
            }
            currentStatement.clear();
        } else {
            currentStatement += currentChar;
        }
        
        prevChar = currentChar;
    }
    
    // 处理最后一个语句（如果没有以分号结尾）
    QString trimmedStatement = currentStatement.trimmed();
    if (!trimmedStatement.isEmpty()) {
        statements.append(trimmedStatement);
    }
    
    return statements;
}

bool SqlFileReaderDuck::isResourcePath(const QString &filePath) {
    return filePath.startsWith(":/");
}

void SqlFileReaderDuck::setLastError(const QString &error) {
    m_lastError = error;
    qWarning() << "SqlFileReaderDuck error:" << error;
}
