# USN Journal 数据库功能

本文档介绍DatabaseDuck模块中新增的USN日志数据库功能。

## 概述

USN（Update Sequence Number）日志数据库功能为FileDuck模块的USN扫描器提供数据存储支持。通过专门的USN数据表和优化的索引设计，实现高效的USN记录存储和查询。

**重要说明**：USN功能完全独立于NTFS MFT扫描功能，使用独立的数据表和API，不依赖file_system_info表。

## 功能独立性

### USN vs NTFS MFT 对比

| 特性 | USN Journal | NTFS MFT |
|------|-------------|----------|
| **数据来源** | Windows USN Journal | NTFS Master File Table |
| **数据类型** | 文件变更历史记录 | 文件系统静态快照 |
| **主要用途** | 变更监控、审计跟踪 | 文件搜索、磁盘分析 |
| **数据表** | `usn_journal_info` | `file_system_info` |
| **关键字段** | USN、变更原因、文件引用号 | 文件大小、MFT条目、时间戳 |
| **查询模式** | 按USN范围、变更类型 | 按文件名、路径、大小 |

## 数据库表结构

### usn_journal_info 表

USN日志信息主表，存储从Windows USN Journal中提取的文件变更记录：

```sql
CREATE TABLE usn_journal_info (
    id BIGINT PRIMARY KEY,                      -- 主键ID（自增）
    file_name VARCHAR NOT NULL,                 -- 文件名
    file_path VARCHAR NOT NULL,                 -- 文件路径
    file_reference_number BIGINT NOT NULL,      -- 文件引用号
    parent_file_reference_number BIGINT NOT NULL, -- 父文件引用号
    usn BIGINT NOT NULL,                        -- 更新序列号
    time_stamp BIGINT NOT NULL,                 -- 时间戳（Windows FILETIME格式）
    reason INTEGER NOT NULL,                    -- 变更原因
    source_info INTEGER NOT NULL,               -- 源信息
    file_attributes INTEGER NOT NULL,           -- 文件属性
    disk_identifier VARCHAR NOT NULL,           -- 磁盘标识符
    scan_time VARCHAR NOT NULL                  -- 扫描时间
);
```

### usn_scan_statistics 表

USN扫描统计信息表，记录扫描过程的统计数据：

```sql
CREATE TABLE usn_scan_statistics (
    id BIGINT PRIMARY KEY,                      -- 主键ID
    disk_identifier VARCHAR NOT NULL,           -- 磁盘标识符
    scan_start_time VARCHAR NOT NULL,           -- 扫描开始时间
    scan_end_time VARCHAR,                      -- 扫描结束时间
    total_records BIGINT DEFAULT 0,             -- 总记录数
    processed_records BIGINT DEFAULT 0,         -- 已处理记录数
    scan_duration_ms BIGINT DEFAULT 0,          -- 扫描持续时间（毫秒）
    scan_status VARCHAR DEFAULT 'running',      -- 扫描状态
    error_message VARCHAR,                      -- 错误信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 创建时间
);
```

## 索引设计

### 基础索引
- `idx_usn_file_name`: 文件名索引
- `idx_usn_file_path`: 文件路径索引
- `idx_usn_file_ref`: 文件引用号索引
- `idx_usn_number`: USN索引
- `idx_usn_timestamp`: 时间戳索引
- `idx_usn_reason`: 变更原因索引
- `idx_usn_disk_identifier`: 磁盘标识符索引

### 复合索引
- `idx_usn_disk_usn`: 磁盘和USN复合索引
- `idx_usn_disk_time`: 磁盘和时间戳复合索引
- `idx_usn_file_ref_disk`: 文件引用号和磁盘复合索引
- `idx_usn_range_query`: USN范围查询优化索引

## 预定义视图

### 文件操作视图
- `usn_file_created`: 文件创建事件视图
- `usn_file_deleted`: 文件删除事件视图
- `usn_file_renamed`: 文件重命名事件视图
- `usn_file_data_changed`: 文件数据修改事件视图
- `usn_recent_activity`: 最近活动视图（最近24小时）

## API接口

### FileSystemRepositoryDuck 新增方法

#### USN数据插入
```cpp
// 批量插入USN记录
void batchInsertUSN(const std::vector<USNJournalInfoDuck> &usnInfos,
                    std::function<void(const AsyncResultDuck<int> &)> callback = nullptr);

// 并发批量插入USN记录
void concurrentBatchInsertUSN(const std::vector<USNJournalInfoDuck> &usnInfos,
                              std::function<void(const AsyncResultDuck<int> &)> callback = nullptr);
```

#### USN数据查询
```cpp
// 按USN范围搜索
void searchUSNByRange(long long startUsn, long long endUsn,
                      const QString &diskIdentifier = QString(),
                      std::function<void(const AsyncResultDuck<std::vector<USNJournalInfoDuck> > &)> callback = nullptr);

// 按变更原因搜索
void searchUSNByReason(unsigned int reason,
                       const QString &diskIdentifier = QString(),
                       std::function<void(const AsyncResultDuck<std::vector<USNJournalInfoDuck> > &)> callback = nullptr);

// 获取USN统计信息
void getUSNStatistics(const QString &diskIdentifier = QString(),
                      std::function<void(const AsyncResultDuck<std::tuple<int, long long, long long> > &)> callback = nullptr);
```

#### USN数据管理
```cpp
// 清空指定磁盘的USN数据
void clearUSNData(const QString &diskIdentifier,
                  std::function<void(const AsyncResultDuck<bool> &)> callback = nullptr);
```

## 使用示例

### 基本USN数据插入
```cpp
// 创建USN记录
std::vector<USNJournalInfoDuck> usnInfos;
USNJournalInfoDuck info;
info.setFileName("example.txt");
info.setFilePath("C:\\example.txt");
info.setFileReferenceNumber(12345);
info.setUsn(67890);
info.setReason(0x00000100); // FILE_CREATE
info.setDiskIdentifier("C");
info.updateScanTime();
usnInfos.push_back(info);

// 批量插入
repository->batchInsertUSN(usnInfos, [](const AsyncResultDuck<int> &result) {
    if (result.success) {
        qDebug() << "Inserted" << result.data << "USN records";
    } else {
        qDebug() << "Insert failed:" << result.error;
    }
});
```

### USN数据查询
```cpp
// 按USN范围查询
repository->searchUSNByRange(1000, 2000, "C", [](const auto &result) {
    if (result.success) {
        qDebug() << "Found" << result.data.size() << "USN records";
        for (const auto &info : result.data) {
            qDebug() << "File:" << QString::fromStdString(info.getFileName())
                     << "USN:" << info.getUsn();
        }
    }
});

// 按变更原因查询（文件创建）
repository->searchUSNByReason(0x00000100, "C", [](const auto &result) {
    if (result.success) {
        qDebug() << "Found" << result.data.size() << "file creation events";
    }
});

// 获取统计信息
repository->getUSNStatistics("C", [](const auto &result) {
    if (result.success) {
        auto [totalRecords, minUsn, maxUsn] = result.data;
        qDebug() << "Total records:" << totalRecords
                 << "USN range:" << minUsn << "-" << maxUsn;
    }
});
```

## 数据库配置

### 资源文件配置
在 `DatabaseDuck.qrc` 中为需要USN功能的数据库添加USN表：

```xml
<qresource prefix="/sql/your_database.duckdb">
    <file>sql/file_system_info_duck.sql</file>
    <file>sql/usn_journal_info_duck.sql</file>
</qresource>
```

### 支持USN功能的数据库
当前配置了USN支持的数据库：
- `usn_test.duckdb`: USN专用测试数据库（仅包含USN表）

**注意**：USN功能使用独立的数据库配置，不与NTFS MFT功能共享数据表。

## 性能优化

### DuckDB特有优化
- 禁用进度条：`PRAGMA disable_progress_bar`
- 设置线程数：`SET threads = 4`
- 内存限制：`SET memory_limit = '1GB'`
- 启用对象缓存：`PRAGMA enable_object_cache`

### 批量插入优化
- 使用DuckDB Appender进行高性能批量插入
- 支持并发批处理
- 优化的内存使用和错误处理

### 查询优化
- 针对USN特有查询模式设计的索引
- 预定义视图简化常见查询
- 列式存储优化分析查询

## 注意事项

1. **数据类型**: USN相关的数值字段使用BIGINT类型以支持大数值
2. **时间戳格式**: 使用Windows FILETIME格式存储时间戳
3. **变更原因**: 使用位掩码存储和查询变更原因
4. **磁盘标识符**: 用于区分不同磁盘的USN记录
5. **索引维护**: 大量数据插入时注意索引维护开销

## 扩展功能

未来可能的扩展：
- USN记录压缩和归档
- 实时USN监控
- USN数据分析和报告
- 跨磁盘USN关联分析
