// Copyright 2008 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef MINI_CHROMIUM_BASE_STRINGS_SYS_STRING_CONVERSIONS_H_
#define MINI_CHROMIUM_BASE_STRINGS_SYS_STRING_CONVERSIONS_H_

#include "build/build_config.h"

#if BUILDFLAG(IS_APPLE)

#include <CoreFoundation/CoreFoundation.h>

#include <string>

#if defined(__OBJC__)
#import <Foundation/Foundation.h>
#else
class NSString;
#endif

namespace base {

std::string SysCFStringRefToUTF8(CFStringRef ref);
std::string SysNSStringToUTF8(NSString* nsstring);
CFStringRef SysUTF8ToCFStringRef(const std::string& utf8);
NSString* SysUTF8ToNSString(const std::string& utf8);

}  // namespace base

#endif  // BUILDFLAG(IS_APPLE)

#endif  // MINI_CHROMIUM_BASE_STRINGS_SYS_STRING_CONVERSIONS_H_
