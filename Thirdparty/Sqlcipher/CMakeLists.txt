project(SQLCipher LANGUAGES CXX)

# 版本 4.7.0
# 对标 sqlite 3.49.1

# SQLCipher静态库配置
set(SQLCIPHER_ROOT ${CMAKE_CURRENT_SOURCE_DIR})
set(SQLCIPHER_INCLUDE_DIR ${SQLCIPHER_ROOT}/Include)

# 开发用动态库，发布用静态库

# 创建SQLCipher接口库
add_library(SQLCipher INTERFACE)

# 设置头文件包含路径
target_include_directories(SQLCipher INTERFACE ${SQLCIPHER_INCLUDE_DIR})

# 根据构建类型选择对应的库文件
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    # Debug版本使用动态库
    set(SQLCIPHER_LIB_DIR ${SQLCIPHER_ROOT}/Debug/lib)
    set(SQLCIPHER_LIB ${SQLCIPHER_LIB_DIR}/sqlite3.lib)
else()
    # Release版本使用静态库
    set(SQLCIPHER_LIB_DIR ${SQLCIPHER_ROOT}/Release)
    set(SQLCIPHER_LIB ${SQLCIPHER_LIB_DIR}/libsqlite3.lib)
endif()

# 链接库文件 - 确保 SQLCipher 库优先链接
target_link_libraries(SQLCipher
        INTERFACE
        ${SQLCIPHER_LIB}
)

# 在Windows平台上添加必需的系统库
if(WIN32)
    target_link_libraries(SQLCipher
        INTERFACE
        crypt32     # Windows Cryptography API - SQLCipher需要加密功能
        advapi32    # Advanced Windows API - 用于加密相关功能
    )
endif()

# 设置SQLCipher相关的预处理器定义
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    # Debug版本使用动态库，不定义SQLITE_STATIC
    target_compile_definitions(SQLCipher
        INTERFACE
        SQLITE_HAS_CODEC=1          # 启用SQLCipher加密功能
        SQLITE_TEMP_STORE=2         # 使用内存存储临时数据
        SQLITE_THREADSAFE=1         # 启用线程安全
    )
else()
    # Release版本使用静态库，定义SQLITE_STATIC
    target_compile_definitions(SQLCipher
        INTERFACE
        SQLITE_STATIC               # 静态链接SQLite
        SQLITE_HAS_CODEC=1          # 启用SQLCipher加密功能
        SQLITE_TEMP_STORE=2         # 使用内存存储临时数据
        SQLITE_THREADSAFE=1         # 启用线程安全
    )
endif()

# 定义DLL复制函数
function(add_sqlcipher_dll_copy target_name)
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        # Debug版本需要复制DLL文件到输出目录
        set(SQLCIPHER_BIN_DIR "${CMAKE_SOURCE_DIR}/Thirdparty/Sqlcipher/Debug/bin")

        # 检查DLL文件是否存在
        if(NOT EXISTS "${SQLCIPHER_BIN_DIR}/sqlite3.dll")
            message(WARNING "SQLCipher DLL文件不存在: ${SQLCIPHER_BIN_DIR}/sqlite3.dll")
            return()
        endif()

        add_custom_command(TARGET ${target_name} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different "${SQLCIPHER_BIN_DIR}/sqlite3.dll" "$<TARGET_FILE_DIR:${target_name}>"
            COMMENT "Copying SQLCipher DLL files to ${target_name} output directory"
        )

        message(STATUS "已为目标 ${target_name} 配置SQLCipher DLL复制，源路径: ${SQLCIPHER_BIN_DIR}")
    endif()
endfunction()
