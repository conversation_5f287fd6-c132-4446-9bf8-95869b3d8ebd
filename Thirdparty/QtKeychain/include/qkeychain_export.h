/******************************************************************************
 *   Copyright (C) 2011-2015 <PERSON> <<EMAIL>>      *
 *                                                                            *
 * This program is distributed in the hope that it will be useful, but        *
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY *
 * or FITNESS FOR A PARTICULAR PURPOSE. For licensing and distribution        *
 * details, check the accompanying file 'COPYING'.                            *
 *****************************************************************************/
#ifndef QKEYCHAIN_EXPORT_H
#define QKEYCHAIN_EXPORT_H

#include <QtCore/qglobal.h>

#if !defined(QTKEYCHAIN_NO_EXPORT)
#  ifndef QKEYCHAIN_EXPORT
#    if defined(QKEYCHAIN_STATIC_DEFINE)
#      define QKEYCHAIN_EXPORT
#    else
#      ifndef QKEYCHAIN_EXPORT
#        ifdef qtkeychain_EXPORTS
          /* We are building this library */
#          define QKEYCHAIN_EXPORT Q_DECL_EXPORT
#        else
          /* We are using this library */
#          define QKEYCHAIN_EXPORT Q_DECL_IMPORT
#        endif
#      endif
#    endif
#  endif
#else
#  define QKEYCHAIN_EXPORT
#endif

#endif // QKEYCHAIN_EXPORT_H
