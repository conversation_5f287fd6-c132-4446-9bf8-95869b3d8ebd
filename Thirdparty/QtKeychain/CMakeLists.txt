project(QtKeychain LANGUAGES CXX)

# QtKeychain 静态库配置
set(QTKEYCHAIN_ROOT ${CMAKE_CURRENT_SOURCE_DIR})
set(QTKEYCHAIN_INCLUDE_DIR ${QTKEYCHAIN_ROOT}/include)
set(QTKEYCHAIN_LIB_DIR ${QTKEYCHAIN_ROOT}/lib)

# 创建QtKeychain接口库
add_library(QtKeychain INTERFACE)

# 设置头文件包含路径
target_include_directories(QtKeychain INTERFACE ${QTKEYCHAIN_INCLUDE_DIR})

# 根据构建配置选择库文件
set(QTKEYCHAIN_DEBUG_LIB ${QTKEYCHAIN_LIB_DIR}/debug/qt6keychaind.lib)
set(QTKEYCHAIN_RELEASE_LIB ${QTKEYCHAIN_LIB_DIR}/release/qt6keychain.lib)

# 检查库文件是否存在
if(NOT EXISTS ${QTKEYCHAIN_DEBUG_LIB})
    message(WARNING "QtKeychain debug library not found: ${QTKEYCHAIN_DEBUG_LIB}")
endif()

if(NOT EXISTS ${QTKEYCHAIN_RELEASE_LIB})
    message(WARNING "QtKeychain release library not found: ${QTKEYCHAIN_RELEASE_LIB}")
endif()

# 链接库文件 - 使用生成器表达式根据配置选择正确的库
target_link_libraries(QtKeychain
    INTERFACE
    $<$<CONFIG:Debug>:${QTKEYCHAIN_DEBUG_LIB}>
    $<$<CONFIG:Release>:${QTKEYCHAIN_RELEASE_LIB}>
    $<$<CONFIG:RelWithDebInfo>:${QTKEYCHAIN_RELEASE_LIB}>
    $<$<CONFIG:MinSizeRel>:${QTKEYCHAIN_RELEASE_LIB}>
    Qt6::Core
)

# 在Windows平台上添加必需的系统库
if(WIN32)
    target_link_libraries(QtKeychain
        INTERFACE
        crypt32     # Windows Cryptography API
        advapi32    # Advanced Windows API
    )
endif()

# 设置QtKeychain相关的预处理器定义
target_compile_definitions(QtKeychain
    INTERFACE
    QTKEYCHAIN_NO_EXPORT  # 使用静态库时不需要导出宏
)

