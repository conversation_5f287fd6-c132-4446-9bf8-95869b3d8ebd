project(DuckDB LANGUAGES CXX)

# v1.3.1

# DuckDB 静态库配置
set(DUCKDB_ROOT ${CMAKE_CURRENT_SOURCE_DIR})

# 创建DuckDB接口库
add_library(DuckDB INTERFACE)

# 设置头文件包含路径
target_include_directories(DuckDB INTERFACE
    ${DUCKDB_ROOT}
)

# 链接DuckDB静态库
target_link_libraries(DuckDB INTERFACE
    ${DUCKDB_ROOT}/duckdb.lib
)

# 设置编译器特定选项
if(MSVC)
    target_compile_definitions(DuckDB INTERFACE
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
    )
endif()

# 复制DuckDB DLL到输出目录的函数
function(add_duckdb_dll_copy target_name)
    if(WIN32)
        set(DuckDB_BIN_DIR "${CMAKE_SOURCE_DIR}/Thirdparty/DuckDB")
        # 复制DuckDB DLL到目标输出目录
        add_custom_command(TARGET ${target_name} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
                "${DuckDB_BIN_DIR}/duckdb.dll" "$<TARGET_FILE_DIR:${target_name}>"
            COMMENT "Copying DuckDB DLL to output directory"
        )
    endif()
endfunction()

