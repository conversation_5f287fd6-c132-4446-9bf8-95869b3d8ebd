cmake_minimum_required(VERSION 3.16)

project(TestFindGame VERSION 0.1 LANGUAGES CXX)

# 包含DLL路径设置脚本
include(${CMAKE_SOURCE_DIR}/cmake/SetDllPath.cmake)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

option(DEVELOPMENT "开发环境" ON)

find_package(Qt6 REQUIRED COMPONENTS Quick Test)

qt_standard_project_setup(REQUIRES 6.8)

qt_add_executable(appTestFindGame
    main.cpp
)

qt_add_qml_module(appTestFindGame
    URI TestFindGame
    VERSION 1.0
    QML_FILES
        Main.qml
    RESOURCES
        vcpkg.json
)

# Qt for iOS sets MACOSX_BUNDLE_GUI_IDENTIFIER automatically since Qt 6.1.
# If you are developing for iOS or macOS you should consider setting an
# explicit, fixed bundle identifier manually though.
set_target_properties(appTestFindGame PROPERTIES
#    MACOSX_BUNDLE_GUI_IDENTIFIER com.example.appTestFindGame
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
)

if(WIN32)
    set_target_properties(appTestFindGame PROPERTIES
        LINK_FLAGS "/SUBSYSTEM:WINDOWS"
    )

    # 如果需要管理员权限，可以添加 manifest 文件
    # 或使用 target_link_options
    target_link_options(appTestFindGame PRIVATE
        "/MANIFESTUAC:level='requireAdministrator' uiAccess='false'"
    )
endif()

add_subdirectory(Database)
add_subdirectory(DatabaseDuck)
add_subdirectory(Entity)
add_subdirectory(Thirdparty)
add_subdirectory(Framework)

target_link_libraries(appTestFindGame
    PRIVATE
        Qt6::Quick
        crashpad
        Database
        DatabaseDuck
        Entity
        QtKeychain
        DuckDB
)

# 复制SQLCipher DLL文件（仅在Debug版本时有效）
add_sqlcipher_dll_copy(appTestFindGame)

# 复制DuckDB DLL文件
add_duckdb_dll_copy(appTestFindGame)

# 测试DLL路径检测
set_dll_path(appTestFindGame)

include(GNUInstallDirs)
install(TARGETS appTestFindGame
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)
